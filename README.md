# Vehicle Management API - Python FastAPI Implementation

This is a Python FastAPI implementation of the vehicle management system, equivalent to the PHP `/info` route from the original VehicleV2Controller.

## Project Structure

```
space-proc-user-veh-py/
├── api/                    # API controllers and routes
│   ├── __init__.py
│   └── vehicle_controller.py
├── model/                  # Beanie document models
│   ├── __init__.py
│   └── vehicle_model.py
├── schemas/                # Pydantic schemas for request/response
│   ├── __init__.py
│   └── vehicle_schemas.py
├── manager/                # Business logic managers
│   ├── __init__.py
│   └── vehicle_manager.py
├── services/               # Service layer
│   ├── __init__.py
│   └── vehicle_service.py
├── connector/              # Database connectors
│   ├── __init__.py
│   └── database.py
├── app.py                  # FastAPI application entry point
├── requirements.txt        # Python dependencies
└── README.md              # This file
```

## Features

- **FastAPI Framework**: Modern, fast web framework for building APIs
- **MongoDB Integration**: Using Beanie ODM with Motor async driver
- **Pydantic Validation**: Request/response validation and serialization
- **Clean Architecture**: Separated concerns with controllers, managers, services, and connectors
- **Async/Await**: Full async support for better performance
- **OpenAPI Documentation**: Auto-generated API documentation

## API Endpoints

### GET /v2/vehicles/info

Get detailed vehicle information by VIN or vehicle ID.

**Parameters:**
- **Headers:**
  - `userId` (required): User ID
  - `vin` (optional): Vehicle VIN
- **Query:**
  - `id` (optional): Vehicle ID
  - `country` (required): Country code
  - `language` (required): Language code
  - `source` (optional): Source of request (APP, SPACEWEB)

**Response:**
```json
{
  "success": {
    "vehicleInfo": {
      "vin": "string",
      "lcdv": "string",
      "visual": "string",
      "short_label": "string",
      "nickname": "string",
      "warranty_start_date": 0,
      "attributes": ["string"],
      "type_vehicle": 0,
      "mileage": {
        "value": 0
      }
    },
    "eligibility": ["string"],
    "vehicleProducts": {
      "productsCatalog": ["string"],
      "purchasedProducts": ["string"],
      "productGroupNameStatus": {}
    },
    "settingsUpdate": 0
  }
}
```

## Installation

1. **Install Python dependencies:**
```bash
pip install -r requirements.txt
```

2. **Set up environment variables:**
```bash
export MONGODB_URL="mongodb://localhost:27017"
export DATABASE_NAME="vehicle_db"
export HOST="0.0.0.0"
export PORT="8000"
```

3. **Run the application:**
```bash
python app.py
```

Or using uvicorn directly:
```bash
uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

## Development

### Database Setup

The application uses MongoDB with Beanie ODM. Make sure MongoDB is running and accessible.

### API Documentation

Once the server is running, you can access:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### Health Check

- **Health endpoint**: http://localhost:8000/health
- **Root endpoint**: http://localhost:8000/

## Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `MONGODB_URL` | `mongodb://localhost:27017` | MongoDB connection string |
| `DATABASE_NAME` | `vehicle_db` | Database name |
| `HOST` | `0.0.0.0` | Server host |
| `PORT` | `8000` | Server port |
| `RELOAD` | `true` | Enable auto-reload in development |

## Architecture

### Controllers (api/)
Handle HTTP requests and responses, parameter validation, and route definitions.

### Managers (manager/)
Contain business logic and orchestrate service calls.

### Services (services/)
Handle data access and business operations.

### Models (model/)
Define database document structures using Beanie.

### Schemas (schemas/)
Define request/response models using Pydantic.

### Connectors (connector/)
Handle database connections and initialization.

## Error Handling

The API returns structured error responses:

```json
{
  "error": {
    "message": "Error description",
    "errors": {
      "field": "Field-specific error"
    }
  }
}
```

## Testing

Example curl command to test the `/info` endpoint:

```bash
curl -X GET "http://localhost:8000/v2/vehicles/info?country=US&language=en&id=vehicle123" \
  -H "userId: user123" \
  -H "Content-Type: application/json"
```

## Compatibility

This implementation maintains compatibility with the original PHP VehicleV2Controller `/info` route, including:
- Same request/response structure
- Same validation rules
- Same error handling patterns
- Null value removal for backward compatibility

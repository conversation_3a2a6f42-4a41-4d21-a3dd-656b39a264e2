import logging
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from typing import Optional, Dict, Any, List
from app.helper.response import WSResponse
from app.schemas.vehicleSchemas import ErrorDetail
from app.utils.constants import Constants
from app.services.catalogService import catalogService
from app.services.contribService import contribService
from app.connector.sysSamsDataConnector import sysSamsDataConnector
from app.dataMapper.catalogDataMapper import catalogDataMapper

logger = logging.getLogger(__name__)


class CatalogManager:
    """Singleton manager for catalog-related business logic"""
    
    _instance: Optional['CatalogManager'] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @staticmethod
    async def getCatalogData(params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get catalog data with contribution information"""
        logger.info(f"CatalogManager.getCatalogData with parameters {params}")
        try:
            catalogResponse = await catalogService.fetchCatalogData(params)
            if (
                isinstance(catalogResponse, WSResponse)
                and catalogResponse.statusCode != 200
            ):
                data = catalogResponse.data
                if isinstance(data, dict) and "error" in data:
                    errorData = data["error"]
                    if isinstance(errorData, dict):
                        if 'message' in errorData:
                            raise HTTPException(
                                detail=ErrorDetail(**errorData),
                                status_code=catalogResponse.statusCode,
                            )
                        else:
                            raise HTTPException(
                                detail=ErrorDetail(message=str(errorData)),
                                status_code=catalogResponse.statusCode,
                            )
                    else:
                        raise HTTPException(
                            detail=ErrorDetail(message=str(errorData)),
                            status_code=catalogResponse.statusCode,
                        )
                raise HTTPException(
                    detail=ErrorDetail(message=data if isinstance(data, str) else str(data)),
                    status_code=catalogResponse.statusCode,
                )

            isElectricOrHybrid = await CatalogManager.checkVehicleHorE(params)
            catalogContribResponse = {}
            validProducts = {}
            productIds = []
            catalogProducts = catalogResponse.data.get("success", [])
            for catalogProduct in catalogProducts:
                productId = catalogProduct.get("id", "")
                groupName = await CatalogManager.extractCatalogueGroupName(catalogProduct)
                if groupName != Constants.BUNDLE or (
                    groupName == Constants.LEV and not isElectricOrHybrid
                ):
                    continue
                key = (
                    catalogProduct["groupName"]
                    if groupName == Constants.BUNDLE
                    else groupName
                )
                validProducts[key] = catalogProduct
                if productId:
                    productIds.append(productId)

            contribResponse = []
            if productIds:
                culture = f"{params.get('language', '')}-{params.get('country', '').upper()}"
                contribResponse = await contribService.getContribInfoByProductIds(
                    ",".join(productIds),
                    params.get("brand", ""),
                    culture,
                    params.get("source", "APP"),
                )
            logger.info(f"Response from contrib : {contribResponse}")
            for key, product in validProducts.items():
                ContribData = contribResponse.get(product.get("id", ""), None)
                if not ContribData:
                    raise HTTPException(
                        detail=ErrorDetail(
                            message=f"Contrib data not found for product {product.get('id', '')}"
                        ),
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )
                catalogContribResponse[key] = {"product": product, "contrib": ContribData}

            catalogData = []
            for groupName, productResponse in catalogContribResponse.items():
                catalogProduct = productResponse["product"]
                contribData = productResponse["contrib"]
                catalogProductContribData = await catalogDataMapper.transform(
                    catalogProduct, contribData
                )
                category = (await CatalogManager.getCategory(groupName)).lower()
                source = params.get("source", "")
                if source in ["APP", "SPACEWEB"]:
                    catalogProductContribData["id"] = (
                        catalogProduct.get("groupName", "").lower()
                        if catalogProduct.get("type", "").lower() == Constants.BUNDLE.lower()
                        else ""
                    )
                else:
                    catalogProductContribData["type"] = groupName
                
                catalogProductContribData["category"] = (
                    (await CatalogManager.getCategory(Constants.BUNDLE)).lower()
                    if catalogProduct.get("type", "").lower() == Constants.BUNDLE.lower()
                    else category
                )
                catalogData.append(catalogProductContribData)
            return catalogData
        except HTTPException as e:
            raise
        except Exception as e:
            logger.error(f"CatalogManager.getCatalogData : Caught Exception {str(e)}")
            raise HTTPException(
                detail=ErrorDetail(
                    message=f"CatalogManager.getCatalogData : Caught Exception {str(e)}"
                ),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    
    @staticmethod
    async def checkVehicleHorE(params: Dict[str, Any]) -> bool:
        """Check if vehicle is hybrid or electric"""
        return params.get("type", "") in ["HEV", "PHEV", "BEV", "MHEV", "HFCV"]
    
    @staticmethod
    async def extractCatalogueGroupName(catalogProduct: Dict[str, Any]) -> Optional[str]:
        """Extract catalog group name from product"""
        if catalogProduct.get("groupName", "") == Constants.LEV and (
            catalogProduct.get("familyName", "") == Constants.BEV
            or catalogProduct.get("familyName", "") == Constants.PHEV
        ):
            return Constants.LEV
        elif (
            catalogProduct.get("type", "").upper() == Constants.BUNDLE
            and len(catalogProduct.get("standaloneProducts", [])) > 0
        ):
            return Constants.BUNDLE
        else:
            return None
    
    @staticmethod
    async def getCategory(familyName: Optional[str]) -> str:
        """Get category for a family name"""
        connectedServices = [
            "NAVCOZAR",
            "TMTS",
            "NAVCO",
            "ZAR",
            "LEV",
            "PHEV",
            "BEV",
            "RACCESS",
            "CONNECTEDALARM",
            "DIGITALKEY",
            "AE_CALL",
            "EV_ROUTING_APP",
            "PARTNERSERVICE",
            "TRIPS_IN_THE_CLOUD",
            "STOLEN_VEHICLE",
        ]
        afterSalesServices = ["DIMBO", "PRIVILEGE"]
        
        if familyName in connectedServices:
            return "CONNECTED_SERVICES"
        elif familyName in afterSalesServices:
            return "AFTERSALES_SERVICES"
        else:
            return "OTHERS"


# Create global singleton instance
catalogManager = CatalogManager()

import logging
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from typing import Dict, Any, List
from app.helper.response import WSResponse
from app.schemas.vehicleSchemas import ErrorDetail
from app.utils.constants import Constants
from app.services.subscriptionService import subscriptionService
from app.services.vehicleService import vehicleService
from app.services.contribService import contribService
from app.dataMapper.subscriptionDataMapper import subscriptionDataMapper

logger = logging.getLogger(__name__)


class SubscriptionManager:
    """Singleton manager for subscription-related business logic"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @staticmethod
    async def getSubscriptionData(params: Dict[str, Any], target: str) -> List[Dict[str, Any]]:
        """Get subscription data with contribution information"""
        try:
            subscriptionResponse = await subscriptionService.fetchSubscriptionData(params, target)
            logger.info(f"SubscriptionManager.getSubscriptionData {subscriptionResponse.__dict__}")
            if (isinstance(subscriptionResponse, WSResponse) and subscriptionResponse.statusCode == 404 and subscriptionResponse.data.get("error", {}).get("message") == "Data not found in SAMS and FCA systems"):
                return []
            elif (
                isinstance(subscriptionResponse, WSResponse)
                and subscriptionResponse.statusCode != 200
            ):
                data = subscriptionResponse.data
                if isinstance(data, dict) and "error" in data:
                    errorData = data["error"]
                    if isinstance(errorData, dict):
                        if 'message' in errorData:
                            raise HTTPException(
                                detail=ErrorDetail(**errorData),
                                status_code=subscriptionResponse.statusCode,
                            )
                        else:
                            raise HTTPException(
                                detail=ErrorDetail(message=str(errorData)),
                                status_code=subscriptionResponse.statusCode,
                            )
                    else:
                        raise HTTPException(
                            detail=ErrorDetail(message=str(errorData)),
                            status_code=subscriptionResponse.statusCode,
                        )
                else:
                    raise HTTPException(
                        detail=ErrorDetail(message=data if isinstance(data, str) else str(data)),
                        status_code=subscriptionResponse.statusCode,
                    )
            responseData = subscriptionResponse.data.get("success", {})
            subscriptions = responseData.get('vehicleProvisionings', []) if isinstance(responseData, dict) else []
            subscriptions = await SubscriptionManager.addContribData(params, subscriptions)
            subscriptions = await subscriptionDataMapper.transformSubscriptions(subscriptions)
            return subscriptions
        
        except HTTPException as e:
            raise
        except Exception as e:
            logger.error(f"SubscriptionManager.getSubscriptionData : Caught Exception {str(e)}")
            return []  # Return empty list instead of WSResponse
    
    @staticmethod
    async def addContribData(params: Dict[str, Any], subscriptions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Add contribution data to subscriptions"""
        try:
            if not subscriptions:
                return subscriptions
                
            cultureCode = params['language'] + '-' + params['country']
            productIds = []
            for subscription in subscriptions:
                if (subscription.get("subscription", {}).get("ratePlans") and 
                    len(subscription["subscription"]["ratePlans"]) > 0 and
                    subscription["subscription"]["ratePlans"][0].get("product", {}).get("productCode")):
                    productIds.append(subscription["subscription"]["ratePlans"][0]["product"]["productCode"])
            
            if not productIds:
                return subscriptions
            contribResponse = await contribService.getContribInfoByProductIds(
                ",".join(productIds),
                params.get("brand", ""),
                cultureCode,
                params.get("source", "APP"),
            )
            logger.info(f"Response from contrib : {contribResponse}")
            updatedContracts = []
            for subscription in subscriptions:
                if (subscription.get("subscription", {}).get("ratePlans") and 
                    len(subscription["subscription"]["ratePlans"]) > 0 and
                    subscription["subscription"]["ratePlans"][0].get("product", {}).get("productCode")):
                    productId = subscription["subscription"]["ratePlans"][0]["product"]["productCode"]
                    if productId in contribResponse:
                        contribData = contribResponse[productId]
                        if not isinstance(contribData, dict):
                            # Skip this subscription if contrib data is not valid
                            updatedContracts.append(subscription)
                            continue
                        mergedContract = {**subscription, **contribData}
                        updatedContracts.append(mergedContract)
                    else:
                        # If no contrib data, just add the subscription as is
                        updatedContracts.append(subscription)
                else:
                    updatedContracts.append(subscription)
            return updatedContracts
        except HTTPException as e:
            raise
        except Exception as e:
            logger.error(f"SubscriptionManager.addContribData : Caught Exception {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ErrorDetail(message=str(e))
            )
    
    async def createSubscription(self, subscriptionData: Dict[str, Any]) -> bool:
        """Create a new subscription"""
        # Implementation to be added based on business logic
        return True
    
    async def cancelSubscription(self, subscriptionId: str) -> bool:
        """Cancel a subscription"""
        # Implementation to be added based on business logic
        return True


# Create global singleton instance
subscriptionManager = SubscriptionManager()
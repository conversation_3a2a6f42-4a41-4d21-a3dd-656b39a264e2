import asyncio
import logging
from typing import Optional, Dict, Any, Union
from fastapi import status, HTTPException
from app.helper.response import WSResponse
from app.schemas.vehicleSchemas import (
    ErrorDetail,
    ErrorResponse,
    VehicleInfoResponse,
    VehicleInfoSuccess,
)
from app.services.vehicleService import VehicleService

from app.services.userDataService import userDataService
from app.services.corvetService import corvetService
from app.services.contribService import contribService
from app.helper.vehicleTypeEntities import getVehicleTypeNumber, VEHICLE_TYPES

logger = logging.getLogger(__name__)    

# Forward declarations for manager classes
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from app.manager.catalogManager import CatalogManager
    from app.manager.subscriptionManager import SubscriptionManager
    from app.manager.featureCodesManager import FeatureCodesManager


def remove_null_values(data: Union[Dict, list, Any]) -> Union[Dict, list, Any]:
    """Recursively remove null/None values from dictionaries and lists"""
    if isinstance(data, dict):
        return {k: remove_null_values(v) for k, v in data.items() if v is not None}
    elif isinstance(data, list):
        return [remove_null_values(item) for item in data if item is not None]
    else:
        return data


class VehicleManager:
    """Singleton manager for vehicle-related business logic"""
    
    _instance: Optional['VehicleManager'] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @staticmethod
    async def getVehicleDetail(
        userId: str,
        criteriaValue: str,
        language: str,
        country: str,
        criteriaKey: str = "vin",
        source: str = "APP"
    ) -> Dict[str, Any]:
        """Get vehicle detail information"""
        try:
            logger.info(f"VehicleManager.getVehicleDetail for {criteriaKey}: {criteriaValue}")
            
            userData = await userDataService.getUserData(userId)
            if not userData:
                return ErrorResponse(
                    error=ErrorDetail(message="User not found"),
                    statusCode=status.HTTP_404_NOT_FOUND,
                )
            
            vehicle = await VehicleService.getVehicleFromUserData(userData, criteriaValue, criteriaKey)
            if not vehicle:
                return ErrorResponse(
                    error=ErrorDetail(
                        message="Vehicle not found", statusCode=status.HTTP_404_NOT_FOUND
                    )
                )
            
            updateVehicleFields = {}
            
            userDbId = getattr(userData, "userDbId", None)
            if not userDbId:
                return ErrorResponse(
                    error=ErrorDetail(message="Customer ID not found"),
                    statusCode=status.HTTP_404_NOT_FOUND,
                )
            
            f2mcObj = getattr(userData, "f2mcObj", None)
            
            corvetData = await corvetService.getCorvetData(vehicle.vin)
            if isinstance(corvetData, WSResponse) and corvetData.statusCode != 200:
                data = corvetData.data
                if isinstance(data, (dict, object)) and "error" in data:
                    return ErrorResponse(
                        error=data["error"], statusCode=corvetData.statusCode
                    )
                return ErrorResponse(
                    error=ErrorDetail(message=data if isinstance(data, str) else str(data)),
                    statusCode=corvetData.statusCode
                )
            
            corvetData = corvetData.data.get("success", {})
            vinCheckResult = await corvetService.checkVinExists(corvetData)
            if isinstance(vinCheckResult, ErrorResponse):
                return vinCheckResult
            
            lcdv = await corvetService.getLcdv(corvetData)
            if isinstance(lcdv, bool):
                return ErrorResponse(
                    error=ErrorDetail(message="LCDV not found"),
                    statusCode=status.HTTP_404_NOT_FOUND,
                )
            
            allAttributes = (
                corvetData.get("VEHICULE", {})
                .get("LISTE_ATTRIBUTES_7", {})
                .get("ATTRIBUT", [])
            )
            corvetAttributes = await corvetService.getManagedAttributes(allAttributes)
            
            vehicleType = vehicle.type
            if not vehicle.type:
                vehicleTypeNumber = await getVehicleTypeNumber("DXD", corvetAttributes)
                vehicleType = VEHICLE_TYPES.get(str(vehicleTypeNumber), "")
                updateVehicleFields["type"] = vehicleType
            params = {
                "userId": userId,
                "vin": vehicle.vin,
                "brand": vehicle.brand,
                "country": country,
                "language": language,
                "source": source,
                "userDbId": userDbId,
                'type': vehicleType,
                'includeNonFds': True,
                'lcdv': lcdv,
                'corvetAttributes': corvetAttributes,
                'f2mcObj': f2mcObj
            }
            
            # Import managers here to avoid circular imports
            from app.manager.catalogManager import catalogManager
            from app.manager.subscriptionManager import subscriptionManager
            from app.manager.featureCodesManager import featureCodesManager
            
            # Run all three operations in parallel for better performance
            catalogData, subscriptions, featureCodes = await asyncio.gather(
                catalogManager.getCatalogData(params),
                subscriptionManager.getSubscriptionData(params, 'B2C'),
                featureCodesManager.calculateFeatureCodes(params=params)
            )
            
            responseData = {
                'catalogData': catalogData,
                'subscriptionData': subscriptions,
                'featureCodes': featureCodes,
                'vehicleInfo': {
                    'vin': vehicle.vin,
                    'brand': vehicle.brand,
                    'type': vehicleType,
                    'userDbId': userDbId,
                    'lcdv': lcdv,
                    'corvetAttributes': corvetAttributes
                }
            }

            # Remove null values for backward compatibility
            return remove_null_values(responseData)
            
        except HTTPException:
            # Re-raise HTTPException to let FastAPI handle it
            raise
        except Exception as e:
            logger.error(f"VehicleManager.getVehicleDetail error: {str(e)}")
            return ErrorResponse(
                error=ErrorDetail(message=str(e)),
                statusCode=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


    def __init__(self, vehicle_service: VehicleService):
        self.vehicle_service = vehicle_service
    
    async def get_user_vehicles_data(
        self, 
        user_id: str, 
        brand: str, 
        language: str, 
        country: str
    ) -> Dict[str, Any]:
        """
        Get user vehicles data
        
        Args:
            user_id: User identifier
            brand: Vehicle brand
            language: Language code
            country: Country code
            
        Returns:
            Dictionary containing success response with vehicles list
        """
        try:
            logger.info(
                f"Getting vehicles for user {user_id}, brand: {brand}, "
                f"language: {language}, country: {country}"
            )
            
            # Get vehicles from service
            vehicles = await self.vehicle_service.get_user_vehicles(
                user_id=user_id,
                brand=brand,
                language=language,
                country=country
            )
            
            # Process vehicles to match PHP controller logic
            processed_vehicles = []
            for vehicle in vehicles:
                vehicle_dict = vehicle.to_response_dict()
                
                # Remove empty VIN fields for order vehicles (matching PHP logic)
                if hasattr(vehicle, 'is_order') and vehicle.is_order:
                    if not vehicle_dict.get('vin') or vehicle_dict.get('vin') == '':
                        vehicle_dict.pop('vin', None)
                
                # Ensure command field exists even if empty (backward compatibility)
                if 'command' not in vehicle_dict:
                    vehicle_dict['command'] = ''
                
                processed_vehicles.append(vehicle_dict)
            
            # Remove null values from the response
            final_data = self.remove_null_values({'success': processed_vehicles})
            
            logger.info(f"Successfully retrieved {len(processed_vehicles)} vehicles for user {user_id}")
            
            return final_data
            
        except Exception as e:
            logger.error(f"Error getting user vehicles: {e}", exc_info=True)
            raise
    
    def remove_null_values(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Remove null values from response data (matching PHP removeNullValues method)
        
        Args:
            data: Dictionary to clean
            
        Returns:
            Dictionary with null values removed
        """
        if isinstance(data, dict):
            return {
                key: self.remove_null_values(value)
                for key, value in data.items()
                if value is not None
            }
        elif isinstance(data, list):
            return [
                self.remove_null_values(item)
                for item in data
                if item is not None
            ]
        else:
            return data


# VehicleManager should be instantiated with proper dependency injection
# vehicleManager = VehicleManager()
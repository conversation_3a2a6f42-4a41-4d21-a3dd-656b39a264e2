"""Feature Codes Manager for business logic layer"""
import logging
from typing import Dict, Any, List, Optional
from app.services.featureCodesService import featureCodesService
from app.services.userDataService import userDataService
from app.services.corvetService import corvetService
from app.helper.response import WSResponse
from app.helper.vehicleTypeEntities import getVehicleTypeNumber, VEHICLE_TYPES

logger = logging.getLogger(__name__)


class FeatureCodesManager:
    """Singleton manager for feature codes business logic"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @staticmethod
    async def calculateFeatureCodes(
        params: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        try:
            logger.info(f"Calculating feature codes for VIN: {params['vin']}, userId: {params['userId']}")
            
            # Calculate feature codes
            featureCodes = await featureCodesService.getFeaturesCode(
                params
            )
            
            logger.info(f"Calculated {len(featureCodes)} feature codes for VIN {params['vin']}")
            return featureCodes
            
        except Exception as e:
            logger.error(f"Error calculating feature codes: {str(e)}")
            return []


# Create global singleton instance
featureCodesManager = FeatureCodesManager()
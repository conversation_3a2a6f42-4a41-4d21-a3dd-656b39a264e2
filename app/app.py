from logging import getLogger
import motor
from motor.motor_asyncio import Async<PERSON>MotorClient
from beanie import init_beanie
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import os
from app.api.api import api_router
from app.model.userData import UserData
from app.model.vehicle_model import Vehicle
from app.config.config import config
from app.utils.exceptions import addExceptionHandler
from app.connector.httpClient import HttpClient

# Configure logging
import logging

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)

# Create FastAPI application
app = FastAPI(
    title="Vehicle Management API",
    description="Python FastAPI implementation of vehicle management system with MongoDB",
    version="2.0.0",
    openapi_url="/v2/openapi.json",
)

addExceptionHandler(app)
origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def startupEvent():
    logger.info("Starting the Application Up.")

    # MongoDB connection using config
    try:
        if not config.MONGO_DB_URL or not config.MONGODB_DB:
            logger.warning("MongoDB configuration missing. Running without database connection.")
        else:
            # Create MongoDB client
            client = AsyncIOMotorClient(config.MONGO_DB_URL)
            database = client[config.MONGODB_DB]

            # Initialize Beanie with document models
            await init_beanie(database=database, document_models=[UserData, Vehicle])

            logger.info(f"Connected to MongoDB: {config.MONGODB_DB}")

            # Health check
            await client.admin.command("ping")
            logger.info("Database health check passed")

        logger.info(f"Environment: {config.APP_ENV}")

        # Initialize HTTP client
        await HttpClient.initSession()

    except Exception as e:
        logger.warning(f"Failed to connect to database: {e}. Continuing without database connection.")
        # Don't raise the exception, just log it and continue
        # Initialize HTTP client even if database fails
        try:
            await HttpClient.initSession()
        except Exception as http_e:
            logger.error(f"Failed to initialize HTTP client: {http_e}")


@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on app shutdown."""
    logger.info("Shutting down application...")
    
    # Close HTTP client
    await HttpClient.closeSession()
    
    logger.info("Application shutdown complete.")


app.include_router(api_router)

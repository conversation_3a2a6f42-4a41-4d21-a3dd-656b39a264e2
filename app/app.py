from logging import getLogger
import motor
from motor.motor_asyncio import AsyncIOMotorClient
from beanie import init_beanie
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import os
from app.api.api import api_router
from app.model.userData import UserData
from app.config.config import config
from app.utils.exceptions import addExceptionHandler
from app.connector.httpClient import HttpClient

# Configure logging
import logging

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)

# Create FastAPI application
app = FastAPI(
    title="Vehicle Management API",
    description="Python FastAPI implementation of vehicle management system with MongoDB",
    version="2.0.0",
    openapi_url="/v2/openapi.json",
)

addExceptionHandler(app)
origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def startupEvent():
    logger.info("Starting the Application Up.")
    
    # MongoDB connection using config
    try:
        if not config.MONGO_DB_URL or not config.MONGODB_DB:
            raise ValueError("MongoDB configuration missing. Please set MONGO_DB_URL and MONGODB_DB in .env file")
        
        # Create MongoDB client
        client = AsyncIOMotorClient(config.MONGO_DB_URL)
        database = client[config.MONGODB_DB]
        
        # Initialize Beanie with document models
        await init_beanie(database=database, document_models=[UserData])
        
        logger.info(f"Connected to MongoDB: {config.MONGODB_DB}")
        logger.info(f"Environment: {config.APP_ENV}")
        
        # Initialize HTTP client
        await HttpClient.initSession()
        
        # Health check
        await client.admin.command("ping")
        logger.info("Database health check passed")
        
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on app shutdown."""
    logger.info("Shutting down application...")
    
    # Close HTTP client
    await HttpClient.closeSession()
    
    logger.info("Application shutdown complete.")


app.include_router(api_router)

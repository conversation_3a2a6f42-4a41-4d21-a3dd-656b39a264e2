from dependency_injector import containers, providers
from motor.motor_asyncio import AsyncIOMotorClient

from app.config.config import Config
from app.connector.httpClient import HttpClient
from app.connector.internalHttpClient import InternalHttpClientService
from app.connector.sysCorvetConnector import SysCorvetConnector
from app.connector.sysIdpConnector import SysIdpConnector
from app.connector.sysSamsDataConnector import SysSamsDataConnector
from app.dataMapper.catalogDataMapper import CatalogDataMapper
from app.dataMapper.subscriptionDataMapper import SubscriptionDataMapper
from app.manager.catalogManager import CatalogManager
from app.manager.subscriptionManager import SubscriptionManager
from app.manager.vehicleManager import VehicleManager
from app.manager.featureCodesManager import FeatureCodesManager
from app.services.catalogService import CatalogService
from app.services.contribService import ContribService
from app.services.corvetService import CorvetService
from app.services.subscriptionService import SubscriptionService
from app.services.userDataService import UserDataService
from app.services.vehicleService import VehicleService
from app.services.featureCodesService import FeatureCodesService
from app.services.consumerRightsService import ConsumerRightsService


class Container(containers.DeclarativeContainer):
    """Dependency injection container for the application"""
    
    # Configuration
    config = providers.Singleton(Config)
    
    # Database
    mongoClient = providers.Singleton(
        AsyncIOMotorClient,
        host=config.provided.MONGO_DB_URL
    )
    
    database = providers.Factory(
        lambda client, dbName: client[dbName],
        client=mongoClient,
        dbName=config.provided.MONGODB_DB
    )
    
    # HTTP Client
    httpClient = providers.Singleton(HttpClient)
    
    internalHttpClient = providers.Singleton(
        InternalHttpClientService,
        httpClient=httpClient
    )
    
    # Connectors
    sysCorvetConnector = providers.Factory(
        SysCorvetConnector,
        httpClient=internalHttpClient,
        config=config
    )
    
    sysIdpConnector = providers.Factory(
        SysIdpConnector,
        httpClient=internalHttpClient,
        config=config
    )
    
    sysSamsDataConnector = providers.Factory(
        SysSamsDataConnector,
        httpClient=internalHttpClient,
        config=config
    )
    
    # Data Mappers
    catalogDataMapper = providers.Factory(
        CatalogDataMapper
    )
    
    subscriptionDataMapper = providers.Factory(
        SubscriptionDataMapper
    )
    
    # Services
    vehicleService = providers.Factory(
        VehicleService
    )
    
    catalogService = providers.Factory(
        CatalogService,
        sysSamsDataConnector=sysSamsDataConnector
    )
    
    subscriptionService = providers.Factory(
        SubscriptionService,
        sysSamsDataConnector=sysSamsDataConnector
    )
    
    userDataService = providers.Factory(
        UserDataService
    )
    
    corvetService = providers.Factory(
        CorvetService,
        sysCorvetConnector=sysCorvetConnector
    )
    
    contribService = providers.Factory(
        ContribService,
        sysSamsDataConnector=sysSamsDataConnector
    )

    consumerRightsService = providers.Factory(
        ConsumerRightsService,
        sysIdpConnector=sysIdpConnector
    )
    
    featureCodesService = providers.Factory(
        FeatureCodesService,
        consumerRightsService=consumerRightsService,
        cdnUrl=config.provided.SETTINGS_CDN_URL
    )
    
    # Managers
    catalogManager = providers.Factory(
        CatalogManager,
        catalogService=catalogService,
        sysSamsDataConnector=sysSamsDataConnector,
        contribService=contribService,
        catalogDataMapper=catalogDataMapper
    )
    
    subscriptionManager = providers.Factory(
        SubscriptionManager,
        subscriptionService=subscriptionService,
        vehicleService=vehicleService,
        contribService=contribService,
        subscriptionDataMapper=subscriptionDataMapper
    )
    
    featureCodesManager = providers.Factory(
        FeatureCodesManager,
        featureCodesService=featureCodesService,
        userDataService=userDataService,
        corvetService=corvetService
    )
    
    vehicleManager = providers.Singleton(
        VehicleManager,
        vehicleService=vehicleService,
        userDataService=userDataService,
        corvetService=corvetService,
        contribService=contribService,
        catalogManager=catalogManager,
        subscriptionManager=subscriptionManager,
        featureCodesManager=featureCodesManager,
    )


# Create global container instance
container = Container()

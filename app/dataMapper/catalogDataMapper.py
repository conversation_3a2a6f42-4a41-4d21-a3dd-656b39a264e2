from typing import Dict, Any, List, Optional
import logging

class CatalogDataMapper:
    """Data mapper for catalog data transformation"""
    
    def __init__(self) -> None:
        pass
    
    async def transform(self, item: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform catalog item with contribution data"""
        self.item = item
        return {
            "catalogId": self.item.get("id"),
            "marketingProductSheetId": self.item.get("marketingProductSheetId"),
            "price": (await self.getPrice(item)).get("amount"),
            "title": data.get("title"),
            "shortDescription": data.get("shortDescription", ""),
            "homePage": data.get("homePage", ""),
            "productUrl": data.get("productUrl"),
            "productUrlSso": data.get("productUrlSso"),
            "fullDescription": data.get("fullDescription"),
            "topMainImage": data.get("topMainImage", ""),
            "urlCvs": data.get("productUrlCvs", ""),
            "type": "bundle" if item.get("type", "").upper() == "BUNDLE" else "product",
            "offer": await self.getOfferWithPrice(item),
            "isBundle": True,
            "bundleId": item.get("bundleId"),
            'productGroupName': item.get("familyName", "")
        }

    async def getPrice(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Get price from item offers"""
        offers = item.get("offers")
        if offers:
            offer = offers[0] if isinstance(offers, list) else offers
            return await self.getPriceFromOffer(offer)
        return {"amount": 0, "currency": ""}

    async def getPriceFromOffer(self, offer: Dict[str, Any]) -> Dict[str, Any]:
        """Extract price from offer"""
        prices = offer.get("prices")
        if prices:
            price = prices[0] if isinstance(prices, list) else prices
            return {
                "amount": price.get("price", 0) or 0,
                "currency": price.get("currency", ""),
            }
        return {"amount": 0, "currency": ""}

    async def getOfferWithPrice(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Get offer with price information"""
        offers = item.get("offers", [])
        offer: Optional[Dict[str, Any]] = None

        # Filter offers with priority
        offers_with_priority = [o for o in offers if o.get("priority") is not None]

        if offers_with_priority:
            offers_with_priority.sort(key=lambda x: x["priority"])
            offer = offers_with_priority[0]
        else:
            # Check for periodical offer
            offerPeriodical = await self.getOfferPeriodical(item)
            if offerPeriodical:
                offer = offerPeriodical
            else:
                offer = offers[0] if offers else {}

        result = {
            "pricingModel": offer.get("pricingModel", ""),
            "fromPrice": offer.get("fromPrice") or 0,
            "price": await self.getOfferPrice(offer),
            "isFreetrial": offer.get("isFreetrial", 0) or 0,
            "freeTrialDuration": offer.get("freeTrialDuration"),
            "freeTrialDurationType": offer.get("freeTrialDurationType"),
        }
        return result

    async def getOfferPeriodical(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get periodical offer from item"""
        for offer in item.get("offers", []):
            if offer.get("pricingModel") == "Periodical":
                return offer
        return None

    async def getOfferPrice(self, offer: Dict[str, Any]) -> Dict[str, Any]:
        """Get price details from offer"""
        prices = offer.get("prices", [])
        price = prices[0] if prices else {}
        return {
            "periodType": price.get("periodType", "") or "",
            "price": price.get("price", 0) or 0,
            "currency": price.get("currency", ""),
            "typeDiscount": price.get("typeDiscount", ""),
        }


# Create global singleton instance
catalogDataMapper = CatalogDataMapper()

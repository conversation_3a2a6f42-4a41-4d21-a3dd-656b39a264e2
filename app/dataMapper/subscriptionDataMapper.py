from typing import Any, Dict, List, Optional, Union
import logging
from fastapi import HTTPException, status
from app.schemas.subscription import SubscriptionModel, ContractRemoteLev, DateRange
from app.helper.response import WSResponse
from app.schemas.vehicleSchemas import ErrorDetail

logger = logging.getLogger(__name__)


class SubscriptionDataMapper:
    """Data mapper for subscription data transformation"""
    
    def __init__(self) -> None:
        pass
    
    async def calculateDuration(self, samsDuration: str) -> str:
        """Calculate duration based on SAMS duration"""
        duration = ''
        if samsDuration and samsDuration.isdigit():
            samsDurationInt = int(samsDuration)
            duration = 'per year' if (samsDurationInt % 12) >= 1 else 'per month'
        return duration
    
    async def getCategory(self, familyName: Optional[str]) -> str:
        """Get category based on family name"""
        connectedServices = [
            'NAVCOZAR', 'TMTS', 'NAVCO', 'ZAR', 'LEV', 'PHEV', 'BEV', 
            'RACCESS', 'CONNECTEDALARM', 'DIGITALKEY', 'AE_CALL', 
            'EV_ROUTING_APP', 'PARTNERSERVICE', 'TRIPS_IN_THE_CLOUD', 'STOLEN_VEHICLE'
        ]
        afterSalesServices = ['DIMBO', 'PRIVILEGE']
        
        if familyName in connectedServices:
            return 'CONNECTED_SERVICES'
        elif familyName in afterSalesServices:
            return 'AFTERSALES_SERVICES'
        else:
            return 'OTHERS'
    
    async def samsStatusToMymStatus(self, samsStatus: str) -> Union[int, bool]:
        """Convert SAMS status to MYM status"""
        statusUpper = samsStatus.upper() if samsStatus else ''
        
        statusMapping = {
            'ACTIVATED': SubscriptionModel.CONTRACT_ACTIVE,
            'PENDING EXTENSION': SubscriptionModel.CONTRACT_ACTIVE,
            'PENDING ACTIVATION': ContractRemoteLev.CONTRACT_SAMS_PENDING_ACTIVATION,
            'PENDING IDENTIFICATION': ContractRemoteLev.CONTRACT_SAMS_PENDING_IDENTIFICATION,
            'PENDING SUBSCRIPTION': ContractRemoteLev.CONTRACT_SAMS_PENDING_SUBSCRIPTION,
            'EN ATTENTE DE SOUSCRIPTION': ContractRemoteLev.CONTRACT_SAMS_PENDING_SUBSCRIPTION,
            'CANCELLED': ContractRemoteLev.CONTRACT_SAMS_CANCELLED,
            'PENDING CANCELLATION': ContractRemoteLev.CONTRACT_SAMS_PENDING_CANCELLATION,
            'EXPIRED': ContractRemoteLev.CONTRACT_SAMS_EXPIRED,
            'EXPIRED IN': ContractRemoteLev.CONTRACT_SAMS_EXPIRED_IN,
            'TERMINATED': ContractRemoteLev.CONTRACT_SAMS_TERMINATED,
            'PENDING TERMINATION': ContractRemoteLev.CONTRACT_SAMS_PENDING_TERMINATION,
            'FAILED PAYMENT': ContractRemoteLev.CONTRACT_SAMS_FAILED_PAYMENT,
            'DEACTIVATED': ContractRemoteLev.CONTRACT_SAMS_DEACTIVATED,
            'PENDING DEACTIVATION': ContractRemoteLev.CONTRACT_SAMS_PENDING_DEACTIVATION,
        }
        
        return statusMapping.get(statusUpper, False)
    
    async def isMorePrior(self, currentOne: SubscriptionModel, oldOne: Optional[SubscriptionModel]) -> bool:
        """Check if current subscription is more prior than old one"""
        if not oldOne:
            return True
            
        if currentOne.getPriority() == oldOne.getPriority():
            currentValidity = currentOne.getValidity()
            oldValidity = oldOne.getValidity()
            
            if oldValidity and currentValidity:
                if oldValidity.start < currentValidity.start:
                    return True
                elif oldValidity.start == currentValidity.start:
                    if oldOne.subscriptionTechCreationDate < currentOne.subscriptionTechCreationDate:
                        return True
        
        currentPriority = currentOne.getPriority()
        oldPriority = oldOne.getPriority()
        
        if currentPriority and oldPriority:
            return currentPriority < oldPriority
        
        return False
    
    async def mapSubscription(
        self,
        subscriptionModel: SubscriptionModel, 
        subscription: Dict[str, Any], 
        status: int, 
        isLev: Optional[bool] = False
    ) -> SubscriptionModel:
        """Map subscription data to subscription model"""
        
        # Handle periodical pricing model
        ratePlans = subscription.get('subscription', {}).get('ratePlans', [])
        if ratePlans and ratePlans[0].get('pricingModel') == 'Periodical':
            subscription['subscriptionEndDate'] = None
        
        # Get category and create date range
        productFamily = ratePlans[0].get('product', {}).get('productFamily') if ratePlans else None
        category = await self.getCategory(productFamily)
        dateRange = DateRange(subscription.get('startDate'), subscription.get('endDate'))
        
        # Set basic properties
        subscriptionModel.setType(productFamily or '')
        subscriptionModel.setStatus(status)
        subscriptionModel.setValidity(dateRange)
        subscriptionModel.setIsExtensible(subscription.get('subscription', {}).get('isExtensible', False))
        subscriptionModel.setTitle(subscription.get('title', ''))
        subscriptionModel.setCategory(category)
        subscriptionModel.setActive()
        
        hasFreeTrial = subscription.get('subscription', {}).get('hasFreeTrial', '')
        subscriptionModel.setHasFreeTrial(str(hasFreeTrial).lower() == 'true')
        
        # Handle LEV specific logic
        if isLev:
            if productFamily != 'RACCESS':
                subscriptionModel.setType('REMOTELEV')
            
            # Set FDS codes
            if productFamily == 'RACCESS':
                subscriptionModel.setFds(['NEE02', 'NEF02', 'NEF01'])
            else:
                subscriptionModel.setFds(['NCG01', 'NBM01', 'NAS01', 'NAO02'])
            
            subscriptionModel.setAssociationId(subscription.get('associationId', '') or "")
        else:
            subscriptionModel.setAssociationId(subscription.get('associationId', '') or "")
        
        # Set remaining properties
        subscriptionModel.setAssociationLevel(subscription.get('associationLevel', ''))
        subscriptionModel.setStatusReason(subscription.get('statusReason', ''))
        subscriptionModel.setSubscriptionTechCreationDate(subscription.get('techCreationDate', ''))
        subscriptionModel.setPriority(subscription.get('subscription', {}).get('status', ''))
        subscriptionModel.setTopMainImage(subscription.get('topMainImage', ''))
        subscriptionModel.setUrlSso(subscription.get('productUrlSso', ''))
        subscriptionModel.setUrlCvs(subscription.get('productUrlCvs', ''))
        
        return subscriptionModel
    
    async def transformSubscriptions(self, samsSubscriptions: List[Dict[str, Any]]) -> Union[List[SubscriptionModel], WSResponse]:
        """Transform SAMS subscriptions to flat list of SubscriptionModel objects"""
        try:
            subscriptions = {}
            
            for subscription in samsSubscriptions:
                subscriptionStatus = subscription.get('subscription', {}).get('status', '')
                status = await self.samsStatusToMymStatus(subscriptionStatus)
                
                # Handle pending identification without association ID
                if (not subscription.get('associationId') and 
                    status == ContractRemoteLev.CONTRACT_SAMS_PENDING_IDENTIFICATION):
                    status = ContractRemoteLev.CONTRACT_SAMS_PENDING_SUBSCRIPTION
                
                if status and status > 0:
                    ratePlans = subscription.get('subscription', {}).get('ratePlans', [])
                    productFamily = ratePlans[0].get('product', {}).get('productFamily') if ratePlans else None
                    if productFamily:
                        typeKey = productFamily.lower()
                        samsSubscription = await self.mapSubscription(SubscriptionModel(), subscription, status)
                        
                        if typeKey not in subscriptions:
                            subscriptions[typeKey] = [None]
                        
                        if await self.isMorePrior(samsSubscription, subscriptions[typeKey][0]):
                            subscriptions[typeKey][0] = samsSubscription
            
            # Flatten the dictionary to a list of SubscriptionModel objects
            subscriptionList = []
            for typeKey, subscriptionArray in subscriptions.items():
                for subscription in subscriptionArray:
                    if subscription is not None and isinstance(subscription, SubscriptionModel):
                        subscriptionList.append(subscription.__dict__)
            
            return subscriptionList
            
        except Exception as e:
            logger.error(f"SubscriptionDataMapper.transformSubscriptions error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ErrorDetail(message=str(e))
            )


# Create global singleton instance
subscriptionDataMapper = SubscriptionDataMapper()

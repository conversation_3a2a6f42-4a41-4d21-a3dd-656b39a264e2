"""SPSEligibility Repository for database operations"""
import logging
from typing import List, Dict, Any, Optional
from app.model.spsEligibility import SPSEligibility

logger = logging.getLogger(__name__)


class SPSEligibilityRepository:
    """Repository for SPSEligibility operations"""
    
    async def getSPSEligibilityData(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get SPS eligibility data grouped by scope (LCDV and MODEL)"""
        try:
            # Get LCDV scope eligibility rules
            lcdv_eligibility = await SPSEligibility.find({"scope": "LCDV"}).to_list()
            
            # Get MODEL scope eligibility rules  
            model_eligibility = await SPSEligibility.find({"scope": "MODEL"}).to_list()
            
            # Convert to arrays for backward compatibility
            lcdv_array = []
            for eligibility in lcdv_eligibility:
                lcdv_array.append(eligibility.dict(by_alias=True))
            
            model_array = []
            for eligibility in model_eligibility:
                model_array.append(eligibility.dict(by_alias=True))
            
            return {
                'lcdv': lcdv_array,
                'model': model_array,
            }
            
        except Exception as e:
            logger.error(f"Error fetching SPS eligibility data: {str(e)}")
            raise e
    
    async def findByScope(self, scope: str) -> List[SPSEligibility]:
        """Find eligibility records by scope"""
        try:
            return await SPSEligibility.find({"scope": scope}).to_list()
        except Exception as e:
            logger.error(f"Error finding SPS eligibility by scope {scope}: {str(e)}")
            return []

class Constants:
    """Constants for the Space Middleware API"""
    
    # API Configuration
    VEHICLE_API_VERSION: str = "/v2"
    VEHICLE_API_BASE_URL: str = "/vehicles"
    VEHICLE_API: str = f"{VEHICLE_API_VERSION}{VEHICLE_API_BASE_URL}"
    VEHICLE_API_TAG: str = "Vehicles"
    VEHICLE_API_TITLE: str = "Vehicle Management API"
    VEHICLE_API_DESCRIPTION: str = "API for Vehicle Information Management"
    
    # Authentication
    AUTHENTICATED: str = "authenticated"
    
    # Default Values
    DEFAULT_PAGE: int = 1
    DEFAULT_SIZE: int = 50
    MINIMUM_SIZE: int = 1
    
    # Response Messages
    VEHICLE_NOT_FOUND: str = "Vehicle not found"
    USER_NOT_FOUND: str = "User not found"
    VALIDATION_FAILED: str = "Validation failed"
    MISSING_CRITERIA: str = "Either VIN or vehicle ID must be provided"
    MISSING_MONGODB_CONFIG: str = "MongoDB configuration missing. Please set MONGO_DB_URL and MONGODB_DB in .env file"
    
    # API Summaries
    SUMMARY_VEHICLE_INFO_GET: str = "Get detailed vehicle information by VIN or vehicle ID"
    
    # Source Types
    SOURCE_APP: str = "APP"
    SOURCE_SPACEWEB: str = "SPACEWEB"
    
    # Operators
    EQUAL: str = "eq"
    
    # Common Strings
    EMPTY_STRING: str = ""

    LEV = "LEV"
    BEV = "BEV"
    PHEV = "PHEV"
    BUNDLE = "BUNDLE"

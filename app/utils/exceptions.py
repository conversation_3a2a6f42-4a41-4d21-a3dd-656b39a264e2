from logging import getLogger

from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.schemas.vehicleSchemas import ErrorDetail

log = getLogger(__name__)


def addExceptionHandler(app):
    @app.exception_handler(HTTPException)
    async def HTTPExceptionHandler(request: Request, exc: HTTPException):
        # Handle ErrorDetail objects in detail
        if isinstance(exc.detail, ErrorDetail):
            return JSONResponse(
                content={'error': exc.detail.model_dump()},
                status_code=exc.status_code,
            )
        # Handle string details
        return JSONResponse(
            content={'error': {'message': str(exc.detail)}},
            status_code=exc.status_code,
        )
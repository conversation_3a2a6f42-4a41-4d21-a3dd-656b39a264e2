from enum import Enum
from typing import Optional

VEHICLE_TYPES: dict[str, str] = {
    "00": "ICE",
    "02": "HEV",
    "03": "PHEV",
    "04": "BEV",
    "05": "MHEV",
    "06": "HFCV",
}

ENGINE_TYPES: dict[str, str] = {
    "ICE": "ICE",
    "HEV": "HEV",
    "PHEV": "PHEV",
    "BEV": "BEV",
    "MHEV": "MHEV",
    "HFCV": "HYDROGEN",
}

PLUG_TYPES: dict[str, Optional[str]] = {
    "00": None,
    "01": "CCS1",
    "02": "CCS2",
    "03": "GBT",
    "04": "CHADEMO",
    "05": "Type1",
    "06": "Type2",
}


async def getVehicleTypeNumber(attribute_code: str, corvet_attributes: list[str]) -> str | int:
    """
    Replicates the PHP getType() logic:
    - Looks for a value in corvet_attributes starting with attribute_code
    - Returns the 2 characters after the code if found
    - Returns -1 if not found
    """
    for value in corvet_attributes:
        if value.startswith(attribute_code):
            return value[3:5]
    return -1
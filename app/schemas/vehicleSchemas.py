from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from enum import Enum


class SourceEnum(str, Enum):
    APP = "APP"
    SPACEWEB = "SPACEWEB"


class VehicleInfoRequest(BaseModel):
    userId: str = Field(..., description="User ID from header")
    vin: Optional[str] = Field(None, description="Vehicle VIN from header")
    id: Optional[str] = Field(None, description="Vehicle ID from query")
    country: str = Field(..., description="Country code")
    language: str = Field(..., description="Language code")
    source: Optional[SourceEnum] = Field(None, description="Source of request")

    class Config:
        populate_by_name = True


class MileageInfo(BaseModel):
    value: int


class VehicleInfo(BaseModel):
    vin: str
    lcdv: str
    visual: str
    shortLabel: Optional[str] = Field(None, alias="short_label")
    nickname: Optional[str] = None
    warrantyStartDate: int = Field(alias="warranty_start_date")
    attributes: List[str]
    typeVehicle: int = Field(alias="type_vehicle")
    mileage: MileageInfo
    
    class Config:
        populate_by_name = True


class VehicleProducts(BaseModel):
    availableProducts: List[Dict[str, Any]] = Field(default_factory=list, alias="available_products")
    purchasedProducts: List[Dict[str, Any]] = Field(default_factory=list, alias="purchased_products")
    productGroups: List[Dict[str, Any]] = Field(default_factory=list, alias="product_groups")
    eligibility: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        populate_by_name = True


class VehicleInfoSuccess(BaseModel):
    vehicleInfo: VehicleInfo = Field(alias="vehicle_info")
    eligibility: List[str]
    vehicleProducts: VehicleProducts = Field(alias="vehicle_products")
    settingsUpdate: int = Field(alias="settings_update")

    class Config:
        populate_by_name = True


class VehicleInfoResponse(BaseModel):
    success: VehicleInfoSuccess


class ErrorDetail(BaseModel):
    message: str
    errors: Optional[Any] = None


class ErrorResponse(BaseModel):
    error: ErrorDetail
    statusCode: int

class BrandEnum(str, Enum):
    """Supported vehicle brands"""
    AC = "AC"
    AP = "AP"
    DS = "DS"
    OP = "OP"
    VX = "VX"
    SP = "SP"
    FT = "FT"
    FO = "FO"
    AH = "AH"
    AR = "AR"
    CY = "CY"
    DG = "DG"
    JE = "JE"
    LA = "LA"
    RM = "RM"
    MA = "MA"
    OV = "OV"

class VehicleResponse(BaseModel):
    """Individual vehicle response schema"""
    vin: Optional[str] = Field(None, description="Vehicle Identification Number")
    lcdv: Optional[str] = Field(None, description="LCDV code")
    visual: Optional[str] = Field(None, description="Visual identifier")
    short_label: Optional[str] = Field(None, description="Short label")
    nickname: Optional[str] = Field(None, description="Vehicle nickname")
    warranty_start_date: Optional[int] = Field(None, description="Warranty start date timestamp")
    command: Optional[str] = Field(None, description="Command field")
    sdp: str = Field(..., description="SDP identifier")

class GetUserVehiclesResponse(BaseModel):
    """Response schema for getting user vehicles"""
    success: List[VehicleResponse] = Field(..., description="List of user vehicles")


class ErrorDetail(BaseModel):
    """Error detail schema"""
    message: str = Field(..., description="Error message")
    errors: Dict[str, Any] = Field(default_factory=dict, description="Validation errors")


class ErrorResponse(BaseModel):
    """Error response schema"""
    error: ErrorDetail = Field(..., description="Error details")
    statusCode: int = Field(..., description="HTTP status code")


class ValidationErrorResponse(BaseModel):
    """Validation error response schema"""
    error: Dict[str, Any] = Field(..., description="Validation error details")
    
    class Config:
        schema_extra = {
            "example": {
                "error": {
                    "message": "validation_failed",
                    "errors": {
                        "userId": "This value should not be blank."
                    }
                }
            }
        }


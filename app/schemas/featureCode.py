"""Feature Code constants and model"""
from typing import Optional, Dict, Any
from pydantic import BaseModel


class FeatureCodeConstants:
    """Feature code related constants"""
    
    # Brand constants
    BRAND_JEEP = 'JE'
    BRAND_ALFA = 'AR'
    BRAND_FIAT = 'FT'
    
    # Protocol constants
    J4U_NAV_PROTOCOL = "ble"
    FIAT_NAV_PROTOCOL = "network"
    J4U_TRIPS_PROTOCOL = "network"
    
    # Engine types
    BATTERY_ELECTRIC_VEHICLE = 'BEV'
    
    # Digital key types
    J4U_DIGITAL_KEY_TYPE = "NEA"
    A5U_DIGITAL_KEY = "Brain"
    LEGACY_XF_DIGITAL_KEY_TYPE = "Atlantis"
    
    # Location types
    J4U_TRIPS_LOCATION = ["trip", "manual"]
    FIAT_TRIPS_LOCATION = ["trip", "manual", "remote"]
    
    # Special codes
    NON_NAW01 = "NON_NAW01"
    NON_FDS_KEY = "ADD_VEHICLE"
    
    # Regions
    REGION_NAFTA = "NAFTA"
    REGION_EMEA = "EMEA"
    
    # SPS eligibility collection
    SPS_ELIGIBILITY_COLLECTION = "boSPSEligibility"
    
    # CSM partners
    CSM_PARTNER_F2MC = "F2MC"
    CSM_PARTNER_TBD = "TBD"
    
    # CSM enrollment statuses
    CSM_ENROLMENT_STATUSES = {
        'NO_PAYMENT_METHOD': 'noPaymentMethod',
        'NO_ACCOUNT_LINKING': 'noAccountLinking',
        'COMPLETE': 'complete',
    }
    
    @classmethod
    def getEnrolmentStatus(cls, key: str) -> Optional[str]:
        """Get enrollment status by key"""
        return cls.CSM_ENROLMENT_STATUSES.get(key)


class FeatureCode(BaseModel):
    """Feature code model"""
    code: str
    status: str
    value: Optional[str] = None
    backend: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    
    class Config:
        arbitrary_types_allowed = True

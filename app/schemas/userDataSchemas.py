"""User Data Schemas - Pydantic BaseModel classes for user data structures"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any


class MileageData(BaseModel):
    """Embedded mileage data model"""
    value: Optional[int] = None
    date: Optional[int] = None
    timestamp: Optional[int] = None
    unit: Optional[str] = "km"


class FeatureCode(BaseModel):
    """Feature code embedded model"""
    code: Optional[str] = None
    status: Optional[str] = None
    value: Optional[str] = None
    backend: Optional[str] = None
    config: Optional[Dict[str, Any]] = None


class Vehicle(BaseModel):
    """Embedded vehicle model within UserData"""
    id: Optional[str] = Field(None, description="Vehicle document ID")
    vin: Optional[str] = Field(None, description="Vehicle Identification Number")
    brand: Optional[str] = Field(None, description="Vehicle brand")
    model: Optional[str] = Field(None, description="Vehicle model")
    modelDescription: Optional[str] = Field(None, description="Model description")
    versionId: Optional[str] = Field(None, description="Version ID")
    picture: Optional[str] = Field(None, description="Vehicle picture URL")
    type: Optional[str] = Field(None, description="Vehicle type (BEV, ICE, etc.)")
    sdp: Optional[str] = Field(None, description="SDP status")
    shortLabel: Optional[str] = Field(None, description="Short label")
    lastUpdate: Optional[int] = Field(None, description="Last update timestamp")
    year: Optional[str] = Field(None, description="Vehicle year")
    country: Optional[str] = Field(None, description="Country code")
    market: Optional[str] = Field(None, description="Market code")
    regTimeStamp: Optional[int] = Field(None, description="Registration timestamp")
    make: Optional[str] = Field(None, description="Vehicle make")
    connectorType: Optional[str] = Field(None, description="Connector type")
    addStatus: Optional[str] = Field(None, description="Add status")
    isO2x: Optional[bool] = Field(None, description="Is O2x vehicle")
    plugType: Optional[list] = Field(None, description="Plug type")
    featureCodeExpiry: Optional[int] = Field(None, description="Feature code expiry")
    visual: Optional[str] = Field(None, description="Visual URL")
    language: Optional[str] = Field(None, description="Language code")
    featureCode: Optional[List[FeatureCode]] = Field(None, description="Feature codes")
    isOrder: Optional[bool] = Field(None, description="Is order vehicle")
    mileage: Optional[MileageData] = Field(None, description="Mileage data")
    nickname: Optional[str] = Field(None, description="User-defined nickname")
    
    class Config:
        validate_by_name = True


class Profile(BaseModel):
    """User profile embedded model"""
    email: Optional[str] = None
    title: Optional[str] = None
    firstName: Optional[str] = Field(None, description="First name")
    lastName: Optional[str] = Field(None, description="Last name")
    phone: Optional[str] = None
    address1: Optional[str] = None
    address2: Optional[str] = None
    city: Optional[str] = None
    zip: Optional[str] = None
    country: Optional[str] = None
    
    class Config:
        validate_by_name = True

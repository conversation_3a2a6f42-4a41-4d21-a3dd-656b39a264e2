from typing import Optional, List, ClassVar, Union
from pydantic import BaseModel, Field
from datetime import datetime


class DateRange(BaseModel):
    """Date range model for subscription validity"""
    start: Optional[int] = Field(None, description="Start date timestamp")
    end: Optional[int] = Field(None, description="End date timestamp")

    def __init__(self, start: Union[int, str, datetime, None] = None, end: Union[int, str, datetime, None] = None, **data):
        processed_start = self._convert_to_timestamp(start)
        processed_end = self._convert_to_timestamp(end)
        super().__init__(start=processed_start, end=processed_end, **data)
    
    def _convert_to_timestamp(self, value: Union[int, str, datetime, None]) -> Optional[int]:
        """Convert various date formats to timestamp"""
        if value is None:
            return None
        elif isinstance(value, datetime):
            return int(value.timestamp())
        elif isinstance(value, str):
            try:
                return int(datetime.fromisoformat(value.replace('Z', '+00:00')).timestamp())
            except ValueError:
                # Try parsing common date formats
                try:
                    return int(datetime.strptime(value, '%Y-%m-%d %H:%M:%S').timestamp())
                except ValueError:
                    try:
                        return int(datetime.strptime(value, '%Y-%m-%d').timestamp())
                    except ValueError:
                        return None
        elif isinstance(value, int):
            return value
        return None
    
    def setStart(self, start: Union[int, str, datetime, None] = None) -> 'DateRange':
        """Set start date"""
        self.start = self._convert_to_timestamp(start)
        return self
    
    def getStart(self) -> Optional[int]:
        """Get start date timestamp"""
        return self.start
    
    def setEnd(self, end: Union[int, str, datetime, None] = None) -> 'DateRange':
        """Set end date"""
        self.end = self._convert_to_timestamp(end)
        return self
    
    def getEnd(self) -> Optional[int]:
        """Get end date timestamp"""
        return self.end


class SubscriptionModel(BaseModel):
    """Base subscription model"""
    # Contract status constants
    CONTRACT_STANDBY: ClassVar[int] = 1
    CONTRACT_ACTIVE: ClassVar[int] = 2
    CONTRACT_SUSPENDED: ClassVar[int] = 3
    CONTRACT_TERMINATED: ClassVar[int] = 4
    CONTRACT_CANCELED: ClassVar[int] = 6
    CONTRACT_TERMINATED_FOR_LOSS: ClassVar[int] = 7
    CONTRACT_EXPIRED: ClassVar[int] = 8
    CONTRACT_STANDBY_TIME: ClassVar[int] = 12
    
    type: Optional[str] = Field(None, description="Subscription type")
    category: Optional[str] = Field(None, description="Subscription category")
    code: Optional[str] = Field(None, description="Subscription code")
    status: Optional[int] = Field(0, description="Subscription status")
    validity: Optional[DateRange] = Field(None, description="Validity date range")
    isExtensible: Optional[bool] = Field(False, description="Is subscription extensible")
    title: Optional[str] = Field(None, description="Subscription title")
    statusReason: Optional[str] = Field(None, description="Status reason")
    subscriptionTechCreationDate: Optional[str] = Field(None, description="Technical creation date")
    hasFreeTrial: Optional[bool] = Field(False, description="Has free trial")
    priority: Optional[int] = Field(None, description="Priority")
    associationId: Optional[str] = Field(None, description="Association ID")
    associationLevel: Optional[str] = Field(None, description="Association level")
    topMainImage: Optional[str] = Field(None, description="Top main image URL")
    urlSso: Optional[str] = Field(None, description="SSO URL")
    urlCvs: Optional[str] = Field(None, description="CVS URL")
    fds: Optional[List[str]] = Field(None, description="FDS codes")
    active: Optional[bool] = Field(False, description="Is subscription active")

    def getType(self) -> Optional[str]:
        return self.type

    def setType(self, type_value: str):
        self.type = type_value

    def getCode(self) -> Optional[str]:
        return self.code

    def setCode(self, code: str):
        self.code = code

    def getStatus(self) -> Optional[int]:
        return self.status

    def setStatus(self, status: int = 0):
        self.status = int(status)

    def isActive(self) -> bool:
        return self.status == self.CONTRACT_ACTIVE

    def getValidity(self) -> Optional[DateRange]:
        return self.validity

    def setValidity(self, validity: DateRange):
        self.validity = validity

    def setIsExtensible(self, is_extensible: bool):
        self.isExtensible = is_extensible

    def setTitle(self, title: str):
        self.title = title

    def getCategory(self) -> Optional[str]:
        return self.category

    def setCategory(self, category: str):
        self.category = category.lower() if category else None

    def setHasFreeTrial(self, has_free_trial: bool):
        self.hasFreeTrial = has_free_trial

    def setAssociationId(self, association_id: str):
        self.associationId = association_id

    def setAssociationLevel(self, association_level: str):
        self.associationLevel = association_level

    def setStatusReason(self, status_reason: str):
        self.statusReason = status_reason

    def setSubscriptionTechCreationDate(self, creation_date: str):
        self.subscriptionTechCreationDate = creation_date

    def getStatusReason(self) -> Optional[str]:
        return self.statusReason

    def getSubscriptionTechCreationDate(self) -> Optional[str]:
        return self.subscriptionTechCreationDate

    def getHasFreeTrial(self) -> Optional[bool]:
        return self.hasFreeTrial

    def getAssociationId(self) -> Optional[str]:
        return self.associationId

    def getAssociationLevel(self) -> Optional[str]:
        return self.associationLevel

    def getTopMainImage(self) -> Optional[str]:
        return self.topMainImage

    def getUrlSso(self) -> Optional[str]:
        return self.urlSso

    def getUrlCvs(self) -> Optional[str]:
        return self.urlCvs

    def setTopMainImage(self, topMainImage: str):
        self.topMainImage = topMainImage

    def setUrlSso(self, urlSso: str):
        self.urlSso = urlSso

    def setUrlCvs(self, urlCvs: str):
        self.urlCvs = urlCvs

    def setFds(self, fds: List[str]):
        self.fds = fds

    def getPriority(self) -> Optional[int]:
        return self.priority
    
    def setActive(self):
        self.active = self.status == self.CONTRACT_ACTIVE
    
    def getActive(self):
        return self.active

    def setPriority(self, subscriptionStatus: str):
        """Set priority based on subscription status"""
        status_upper = subscriptionStatus.upper() if subscriptionStatus else ''
        
        priority_mapping = {
            'PENDING ACTIVATION': 2,
            'PENDING IDENTIFICATION': 1,
            'ACTIVATED': 3,
            'PENDING EXTENSION': 3,
            'PENDING SUBSCRIPTION': 4,
            'EN ATTENTE DE SOUSCRIPTION': 4,
            'CANCELLED': 8,
            'PENDING CANCELLATION': 7,
            'EXPIRED': 6,
            'EXPIRED IN': 5,
            'TERMINATED': 10,
            'PENDING TERMINATION': 9,
            'FAILED PAYMENT': 11,
            'DEACTIVATED': 13,
            'PENDING DEACTIVATION': 12,
        }
        
        self.priority = priority_mapping.get(status_upper, 20)
        return self


class ContractRemoteLev(SubscriptionModel):
    """Contract Remote Lev model extending SubscriptionModel"""
    # Contract status constants for SAMS
    CONTRACT_SAMS_PENDING_ACTIVATION: ClassVar[int] = 101
    CONTRACT_SAMS_PENDING_IDENTIFICATION: ClassVar[int] = 102
    CONTRACT_SAMS_PENDING_SUBSCRIPTION: ClassVar[int] = 103
    CONTRACT_SAMS_CANCELLED: ClassVar[int] = 104
    CONTRACT_SAMS_PENDING_CANCELLATION: ClassVar[int] = 105
    CONTRACT_SAMS_EXPIRED: ClassVar[int] = 106
    CONTRACT_SAMS_EXPIRED_IN: ClassVar[int] = 107
    CONTRACT_SAMS_TERMINATED: ClassVar[int] = 108
    CONTRACT_SAMS_PENDING_TERMINATION: ClassVar[int] = 109
    CONTRACT_SAMS_FAILED_PAYMENT: ClassVar[int] = 110
    CONTRACT_SAMS_DEACTIVATED: ClassVar[int] = 111
    CONTRACT_SAMS_PENDING_DEACTIVATION: ClassVar[int] = 112

    def setFds(self, fds: List[str]):
        """Set FDS codes"""
        self.fds = fds
        return self

    def getFds(self) -> Optional[List[str]]:
        """Get FDS codes"""
        return self.fds


class SamsRatePlanModel(BaseModel):
    """SAMS Rate Plan model"""
    duration: Optional[str] = Field(None, description="Duration")
    pricingModel: Optional[str] = Field(None, description="Pricing model")
    product: Optional[dict] = Field(None, description="Product information")

import logging
from typing import Dict, Any, Optional
from app.helper.response import WSResponse
from app.connector.httpClient import httpClient

logger = logging.getLogger(__name__)


class InternalHttpClientService:
    """Singleton internal HTTP client service that wraps the HttpClient"""
    
    _instance: Optional['InternalHttpClientService'] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
        
    @staticmethod
    async def httpRequest(
        method: str,
        url: str,
        options: Optional[Dict[str, Any]] = None
    ) -> WSResponse:
        """Perform HTTP request using the injected HTTP client"""
        if options is None:
            options = {}
        
        return await httpClient.request(
            method=method,
            url=url,
            headers=options.get("headers"),
            params=options.get("params"),
            json=options.get("json"),
            data=options.get("body")
        )
    
    @staticmethod
    async def get(url: str, options: Optional[Dict[str, Any]] = None) -> WSResponse:
        """GET request"""
        return await InternalHttpClientService.httpRequest("GET", url, options)
    
    @staticmethod
    async def post(url: str, options: Optional[Dict[str, Any]] = None) -> WSResponse:
        """POST request"""
        return await InternalHttpClientService.httpRequest("POST", url, options)
    
    @staticmethod
    async def put(url: str, options: Optional[Dict[str, Any]] = None) -> WSResponse:
        """PUT request"""
        return await InternalHttpClientService.httpRequest("PUT", url, options)
    
    @staticmethod
    async def delete(url: str, options: Optional[Dict[str, Any]] = None) -> WSResponse:
        """DELETE request"""
        return await InternalHttpClientService.httpRequest("DELETE", url, options)
    
    @staticmethod
    async def patch(url: str, options: Optional[Dict[str, Any]] = None) -> WSResponse:
        """PATCH request"""
        return await InternalHttpClientService.httpRequest("PATCH", url, options)


# Create global singleton instance
internalHttpClient = InternalHttpClientService()

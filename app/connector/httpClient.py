import aiohttp
import logging
from typing import Dict, Any, Optional
from app.helper.response import WSResponse
from fastapi import status

logger = logging.getLogger(__name__)


class HttpClient:
    """Singleton HTTP client wrapper for aiohttp with connection pooling"""
    
    _instance: Optional['HttpClient'] = None
    _initialized: bool = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.session = None
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.session: Optional[aiohttp.ClientSession] = None
            self._initialized = True
        
    @staticmethod
    async def initSession():
        """Initialize aiohttp session with optimized settings"""
        instance = HttpClient()
        if instance.session is None or instance.session.closed:
            # Configure connector for better performance
            connector = aiohttp.TCPConnector(
                limit=0,  # Total connection pool size
                limit_per_host=0,  # Max connections per host
                ttl_dns_cache=86400,  # DNS cache for 24 hours
                use_dns_cache=True,  # Enable DNS caching
                keepalive_timeout=86400,  # Keep connections alive for 24 hours
                enable_cleanup_closed=True,
                force_close=False,  # Reuse connections
                ssl=None  # Use default SSL context
            )
            
            # Optimized timeout settings
            timeout = aiohttp.ClientTimeout(
                total=60,  # Total request timeout
                connect=30,  # Connection timeout
                sock_read=30  # Socket read timeout
            )
            
            instance.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'User-Agent': 'Space-Middleware/1.0',
                    'Connection': 'keep-alive'
                }
            )
            logger.info("HTTP client session initialized with optimized settings ✅")
    
    @staticmethod
    async def request(
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        json: Optional[Dict[str, Any]] = None,
        data: Optional[Any] = None
    ) -> WSResponse:
        """Perform HTTP request"""
        instance = HttpClient()
        # Auto-start session if not available
        if instance.session is None or instance.session.closed:
            logger.warning("HTTP client not initialized, starting a new session automatically ⚡")
            await HttpClient.initSession()
        
        try:
            logger.info(f"➡️  {method} {url} | headers={headers} params={params} json={json}")
            
            async with instance.session.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                json=json,
                data=data
            ) as response:
                if response.status != status.HTTP_204_NO_CONTENT:
                    try:
                        body = await response.json()
                    except aiohttp.ContentTypeError:
                        body = await response.text()
                    
                    logger.info(f"⬅️  {method} {url} | status={response.status}")
                    return WSResponse(response.status, body)
                    
                return WSResponse(response.status, {})
                
        except Exception as e:
            logger.error(f"❌ HTTP request failed [{method} {url}]: {e}")
            return WSResponse(500, str(e))
    
    @staticmethod
    async def get(url: str, **kwargs) -> WSResponse:
        """GET request"""
        return await HttpClient.request("GET", url, **kwargs)
    
    @staticmethod
    async def post(url: str, **kwargs) -> WSResponse:
        """POST request"""
        return await HttpClient.request("POST", url, **kwargs)
    
    @staticmethod
    async def put(url: str, **kwargs) -> WSResponse:
        """PUT request"""
        return await HttpClient.request("PUT", url, **kwargs)
    
    @staticmethod
    async def delete(url: str, **kwargs) -> WSResponse:
        """DELETE request"""
        return await HttpClient.request("DELETE", url, **kwargs)
    
    @staticmethod
    async def patch(url: str, **kwargs) -> WSResponse:
        """PATCH request"""
        return await HttpClient.request("PATCH", url, **kwargs)
    
    @staticmethod
    async def closeSession():
        """Close the HTTP session"""
        instance = HttpClient()
        if instance.session and not instance.session.closed:
            await instance.session.close()
            instance.session = None
            logger.info("HTTP client session closed ✅")


# Create global singleton instance
httpClient = HttpClient()

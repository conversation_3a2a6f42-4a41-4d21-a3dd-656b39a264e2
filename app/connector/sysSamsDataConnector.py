import logging
from typing import Dict, Any, Optional
from app.helper.response import WSResponse
from app.connector.internalHttpClient import internalHttpClient
from app.config.config import config

logger = logging.getLogger(__name__)


class SysSamsDataConnector:
    """Singleton connector for SysSams data service"""
    
    _instance: Optional['SysSamsDataConnector'] = None
    _baseUrl: str = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._baseUrl = config.MS_SYS_SAMS_DATA_URL
        return cls._instance
    
    @staticmethod
    async def call(
        method: str,
        uri: str,
        options: Optional[Dict[str, Any]] = None
    ) -> WSResponse:
        """
        Make an async API call to SysSams data service
        
        Args:
            method: HTTP method (GET, POST, etc.)
            uri: API endpoint URI
            options: Request options (headers, data, etc.)
            
        Returns:
            WSResponse object with status code and data
        """
        if options is None:
            options = {}
        
        try:
            fullUrl = f"{SysSamsDataConnector._baseUrl}{uri}"
            logger.info(f"SysSamsDataConnector.call {fullUrl}")
            
            return await internalHttpClient.httpRequest(method, fullUrl, options)
            
        except Exception as e:
            logger.error(f"SysSamsDataConnector.call : Caught Exception {str(e)}")
            return WSResponse(getattr(e, 'code', 500), str(e))
    
    @staticmethod
    async def get(uri: str, options: Optional[Dict[str, Any]] = None) -> WSResponse:
        """GET request"""
        return await SysSamsDataConnector.call("GET", uri, options)
    
    @staticmethod
    async def post(uri: str, options: Optional[Dict[str, Any]] = None) -> WSResponse:
        """POST request"""
        return await SysSamsDataConnector.call("POST", uri, options)


# Create global singleton instance
sysSamsDataConnector = SysSamsDataConnector()

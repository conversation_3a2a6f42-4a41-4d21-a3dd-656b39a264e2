import logging
from typing import Dict, Any, Optional
from app.helper.response import WSResponse
from app.connector.internalHttpClient import internalHttpClient
from app.config.config import config

logger = logging.getLogger(__name__)


class SysCorvetConnector:
    """Singleton connector for System Corvet service"""
    
    _instance: Optional['SysCorvetConnector'] = None
    _baseUrl: str = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._baseUrl = config.MS_SYS_CORVET_DATA_URL
        return cls._instance
    
    @staticmethod
    async def call(
        method: str,
        uri: str,
        options: Optional[Dict[str, Any]] = None
    ) -> WSResponse:
        """
        Make an async API call to System corvet service
        
        Args:
            method: HTTP method (GET, POST, etc.)
            uri: API endpoint URI
            options: Request options (headers, data, etc.)
            
        Returns:
            WSResponse object with status code and data
        """
        if options is None:
            options = {}
        
        try:
            fullUrl = f"{SysCorvetConnector._baseUrl}{uri}"
            logger.info(f"SysCorvetConnector.call {fullUrl}")
            
            return await internalHttpClient.httpRequest(method, fullUrl, options)
            
        except Exception as e:
            logger.error(f"SysCorvetConnector.call : Caught Exception {str(e)}")
            return WSResponse(getattr(e, 'code', 500), str(e))
    
    @staticmethod
    async def get(uri: str, options: Optional[Dict[str, Any]] = None) -> WSResponse:
        """GET request"""
        return await SysCorvetConnector.call("GET", uri, options)
    
    @staticmethod
    async def post(uri: str, options: Optional[Dict[str, Any]] = None) -> WSResponse:
        """POST request"""
        return await SysCorvetConnector.call("POST", uri, options)


# Create global singleton instance
sysCorvetConnector = SysCorvetConnector()

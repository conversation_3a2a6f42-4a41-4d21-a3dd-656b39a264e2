import logging
from typing import Dict, Any, Optional
from app.helper.response import WSResponse
from app.connector.internalHttpClient import internalHttpClient
from app.config.config import config

logger = logging.getLogger(__name__)


class SysIdpConnector:
    """Singleton connector for System IDP service"""
    
    _instance: Optional['SysIdpConnector'] = None
    _baseUrl: str = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._baseUrl = config.MS_SYS_IDP_URL
        return cls._instance
    
    @staticmethod
    async def call(
        method: str,
        uri: str,
        options: Optional[Dict[str, Any]] = None
    ) -> WSResponse:
        """
        Make an async API call to System IDP service
        
        Args:
            method: HTTP method (GET, POST, etc.)
            uri: API endpoint URI
            options: Request options (headers, data, etc.)
            
        Returns:
            WSResponse object with status code and data
        """
        if options is None:
            options = {}
        
        try:
            fullUrl = f"{SysIdpConnector._baseUrl}{uri}"
            logger.info(f"SysIdpConnector.call {fullUrl}")
            
            return await internalHttpClient.httpRequest(method, fullUrl, options)
            
        except Exception as e:
            logger.error(f"SysIdpConnector.call Caught Exception {str(e)}", extra={
                'method': method,
                'url': f"{SysIdpConnector._baseUrl}{uri}",
                'options': options
            })
            return WSResponse(getattr(e, 'code', 500), str(e))
    
    @staticmethod
    async def get(uri: str, options: Optional[Dict[str, Any]] = None) -> WSResponse:
        """GET request"""
        return await SysIdpConnector.call("GET", uri, options)
    
    @staticmethod
    async def post(uri: str, options: Optional[Dict[str, Any]] = None) -> WSResponse:
        """POST request"""
        return await SysIdpConnector.call("POST", uri, options)


# Create global singleton instance
sysIdpConnector = SysIdpConnector()

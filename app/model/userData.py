from beanie import Document
from pydantic import Field
from typing import Optional, List
from app.schemas.userDataSchemas import Profile, Vehicle


class UserData(Document):
    """Main UserData document model matching the actual MongoDB collection"""
    
    userId: str = Field(..., description="User ID")
    userDbId: Optional[str] = Field(None, description="User database ID")
    profile: Optional[Profile] = Field(None, description="User profile")
    vehicle: List[Vehicle] = Field(default_factory=list, description="List of vehicles")
    
    class Settings:
        name = "userData"
        indexes = [
            [("userId", 1)],
            [("userId", 1), ("vehicle.vin", 1)],
            [("userId", 1), ("vehicle.id", 1)],
        ]
    
    class Config:
        validate_by_name = True

"""
Beanie document models for Vehicle
"""
from beanie import Document
from pydantic import Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class Vehicle(Document):
    """Vehicle document model"""
    
    vin: str = Field(..., description="Vehicle Identification Number")
    user_id: str = Field(..., description="User ID")
    lcdv: Optional[str] = Field(None, description="LCDV code")
    visual: Optional[str] = Field(None, description="Visual identifier")
    short_label: Optional[str] = Field(None, description="Short label")
    nickname: Optional[str] = Field(None, description="Vehicle nickname")
    warranty_start_date: Optional[int] = Field(None, description="Warranty start date timestamp")
    command: Optional[str] = Field(None, description="Command field")
    sdp: str = Field(..., description="SDP identifier")
    brand: str = Field(..., description="Vehicle brand")
    country: str = Field(..., description="Country code")
    language: str = Field(..., description="Language code")
    is_order: Optional[bool] = Field(False, description="Is order vehicle")
    mileage: Optional[int] = Field(None, description="Vehicle mileage")
    licence_plate: Optional[str] = Field(None, description="License plate")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Settings:
        name = "vehicles"
        indexes = [
            "user_id",
            "vin",
            [("user_id", 1), ("brand", 1)],
        ]
    
    def to_response_dict(self) -> Dict[str, Any]:
        """Convert to response dictionary format"""
        result = {
            "vin": self.vin,
            "lcdv": self.lcdv,
            "visual": self.visual,
            "short_label": self.short_label,
            "nickname": self.nickname,
            "warranty_start_date": self.warranty_start_date,
            "command": self.command or "",  # Ensure command field exists even if empty
            "sdp": self.sdp
        }
        
        # Remove VIN field for order vehicles with empty VIN
        if self.is_order and (not self.vin or self.vin == ""):
            result.pop("vin", None)
            
        return result

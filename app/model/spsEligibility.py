"""SPSEligibility Beanie Document Model"""
from beanie import Document
from pydantic import Field, ConfigDict
from typing import Optional, List
from bson import ObjectId


class SPSEligibility(Document):
    """SPSEligibility document model for boSPSEligibility collection"""
    
    id: Optional[ObjectId] = Field(None, alias="_id", description="Document ID")
    scope: Optional[str] = Field(None, description="Eligibility scope")
    codes: List[str] = Field(default_factory=list, description="List of eligibility codes")
    eligibilityRule: Optional[str] = Field(None, description="Eligibility rule")
    type: Optional[str] = Field(None, description="Eligibility type")
    eligibilityDisclaimer: Optional[str] = Field(None, description="Eligibility disclaimer text")
    name: Optional[str] = Field(None, description="Name")
    description: Optional[str] = Field(None, description="Description")
    active: Optional[bool] = Field(None, description="Active status")
    
    model_config = ConfigDict(
        validate_assignment=True,
        populate_by_name=True,
        arbitrary_types_allowed=True
    )
    
    class Settings:
        name = "boSPSEligibility"

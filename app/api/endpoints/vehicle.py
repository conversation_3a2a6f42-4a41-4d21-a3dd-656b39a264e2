import time
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Head<PERSON>, Query, HTTPException, status, Depends
from typing import Optional, List
import pycountry
from fastapi.responses import JSONResponse
from typing import Optional
from app.schemas.vehicleSchemas import (
    VehicleInfoRequest,
    VehicleInfoResponse,
    ErrorResponse,
    SourceEnum,
    GetUserVehiclesRequest, 
    GetUserVehiclesResponse,
    ValidationErrorResponse,
    BrandEnum
)
from app.utils.constants import Constants
from app.manager.vehicleManager import VehicleManager
from app.services.vehicleService import VehicleService

from logging import getLogger

logger = getLogger(__name__)
router = APIRouter()


@router.get(
    "/info",
    response_model=dict,
    response_model_by_alias=False,
    status_code=status.HTTP_200_OK,
    summary=Constants.SUMMARY_VEHICLE_INFO_GET,
    description="Get detailed vehicle information by VIN or vehicle ID",
    responses={
        200: {"description": "Successful response", "model": VehicleInfoResponse},
        400: {"description": "Bad Request", "model": ErrorResponse},
        404: {"description": "Vehicle not found", "model": ErrorResponse},
        422: {"description": "Validation Error", "model": ErrorResponse},
        500: {"description": "Internal Server Error", "model": ErrorResponse},
    },
)
async def getVehicleInfo(
    # Header parameters
    userId: str = Header(..., description="User ID"),
    vin: Optional[str] = Header(None, description="Vehicle VIN"),
    id: Optional[str] = Query(None, description="Vehicle ID"),
    country: str = Query(..., description="Country code"),
    language: str = Query(..., description="Language code"),
    source: Optional[SourceEnum] = Query(None, description="Source of request"),
):
    """Get vehicle information endpoint"""
    start_time = time.time()
    logger.info(f"Received GET request for vehicle info - userId: {userId}, vin: {vin}, id: {id}")
    
    # Create request object
    request = VehicleInfoRequest(
        userId=userId, vin=vin, id=id, country=country, language=language, source=source
    )

    # Validate request
    # validationError = await validateRequest(request)
    # if validationError:
    #     logger.debug(f"Validation failed: {validationError.dict()}")
    #     return JSONResponse(
    #         status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
    #         content=validationError.dict()
    #     )

    # Determine criteria
    criteriaValue = id if id else vin
    criteriaKey = "id" if id else "vin"

    if not criteriaValue:
        elapsed_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        logger.info(f"Request failed - Missing criteria. Total time: {elapsed_time:.2f}ms")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"error": {"message": Constants.MISSING_CRITERIA}}
        )

    # Get vehicle details
    logger.debug(f"Getting vehicle detail for {criteriaKey}: {criteriaValue}")
    result = await VehicleManager.getVehicleDetail(
        userId=userId,
        criteriaValue=criteriaValue,
        language=language,
        country=country,
        criteriaKey=criteriaKey,
        source=source.value if source else None,
    )

    # Handle error response
    if isinstance(result, ErrorResponse):
        elapsed_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        logger.info(f"Request failed - Error getting vehicle: {result.error.message}. Total time: {elapsed_time:.2f}ms")
        return JSONResponse(
            status_code=result.dict()['statusCode'],
            content={'error':result.dict()['error']}
        )

    # Remove null values for backward compatibility (like PHP implementation)
    responseDict = result
    
    elapsed_time = (time.time() - start_time) * 1000  # Convert to milliseconds
    logger.info(f"Successfully retrieved vehicle info for {criteriaKey}: {criteriaValue}. Total time: {elapsed_time:.2f}ms")
    return result

async def get_vehicle_manager() -> VehicleManager:
    """Get vehicle manager instance"""
    vehicle_service = VehicleService()
    return VehicleManager(vehicle_service)


@router.get(
    "",
    summary="Get user's vehicles",
    description="Retrieve all vehicles for a specific user filtered by brand, language, and country",
    response_model=GetUserVehiclesResponse,
    responses={
        200: {
            "description": "Successful response",
            "model": GetUserVehiclesResponse
        },
        400: {
            "description": "Bad Request",
            "model": ErrorResponse
        },
        404: {
            "description": "Not Found", 
            "model": ErrorResponse
        },
        422: {
            "description": "Validation Error: Required Fields Missing",
            "model": ValidationErrorResponse
        },
        500: {
            "description": "Internal Server Error",
            "model": ErrorResponse
        }
    },
    tags=["Vehicles"]
)
async def get_user_vehicles(
    user_id: str = Header(..., description="User ID", alias="userId"),
    brand: BrandEnum = Query(..., description="Brand"),
    language: str = Query(..., description="Language"),
    country: str = Query(..., description="Country"),
    vehicle_manager: VehicleManager = Depends(get_vehicle_manager)
):
    """
    Get user's vehicles
    
    Retrieves all vehicles associated with a user, filtered by brand, language, and country.
    Processes the response to handle order vehicles and ensures backward compatibility.
    """
    try:
        # Validate input parameters
        validation_errors = validate_input({
            'userId': user_id,
            'brand': brand.value,
            'language': language,
            'country': country
        })
        
        if validation_errors:
            return JSONResponse(
                status_code=422,
                content={
                    "error": {
                        "message": "validation_failed",
                        "errors": validation_errors
                    }
                }
            )
        
        # Get vehicles data from manager
        result = await vehicle_manager.get_user_vehicles_data(
            user_id=user_id,
            brand=brand.value,
            language=language.lower(),
            country=country.upper()
        )
        
        logger.info(f"Successfully retrieved vehicles for user {user_id}")
        
        return JSONResponse(
            status_code=200,
            content=result
        )
        
    except Exception as e:
        logger.error(f"Error in get_user_vehicles: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "message": "Internal server error",
                    "errors": []
                }
            }
        )


def validate_input(input_data: dict) -> dict:
    """
    Validate input parameters (matching PHP validation logic)
    
    Args:
        input_data: Dictionary containing input parameters
        
    Returns:
        Dictionary of validation errors (empty if valid)
    """
    errors = {}
    
    # Validate userId
    if not input_data.get('userId') or not input_data['userId'].strip():
        errors['userId'] = 'This value should not be blank.'
    
    # Validate brand
    brand = input_data.get('brand', '').upper()
    valid_brands = [b.value for b in BrandEnum]
    if not brand:
        errors['brand'] = 'This value should not be blank.'
    elif brand not in valid_brands:
        errors['brand'] = f'Invalid brand. Must be one of: {", ".join(valid_brands)}'
    
    # Validate language
    language = input_data.get('language', '').lower()
    if not language:
        errors['language'] = 'This value should not be blank.'
    # Note: You might want to add more specific language validation here
    
    # Validate country
    country = input_data.get('country', '').upper()
    if not country:
        errors['country'] = 'This value should not be blank.'
    else:
        # Validate country code using pycountry
        try:
            if not pycountry.countries.get(alpha_2=country):
                errors['country'] = 'Invalid country code.'
        except Exception:
            errors['country'] = 'Invalid country code.'
    
    return errors

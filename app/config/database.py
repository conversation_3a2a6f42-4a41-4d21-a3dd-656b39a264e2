"""
Database configuration and singleton instance for MongoDB connection.
This module provides a centralized database connection that can be imported directly.
"""

import logging
from typing import Op<PERSON>
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from app.config.config import config

logger = logging.getLogger(__name__)


class DatabaseConnection:
    """Singleton database connection manager for MongoDB"""
    
    _instance: Optional['DatabaseConnection'] = None
    _client: Optional[AsyncIOMotorClient] = None
    _database: Optional[AsyncIOMotorDatabase] = None
    _initialized: bool = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @staticmethod
    async def initialize():
        """Initialize the database connection"""
        instance = DatabaseConnection()
        if not instance._initialized:
            try:
                if not config.MONGO_DB_URL or not config.MONGODB_DB:
                    raise ValueError("MongoDB configuration missing. Please set MONGO_DB_URL and MONGODB_DB in .env file")
                
                instance._client = AsyncIOMotorClient(config.MONGO_DB_URL)
                instance._database = instance._client[config.MONGODB_DB]
                
                # Health check
                await instance._client.admin.command("ping")
                instance._initialized = True
                
                logger.info(f"Connected to MongoDB: {config.MONGODB_DB}")
                logger.info("Database health check passed")
                
            except Exception as e:
                logger.error(f"Failed to connect to database: {e}")
                raise
    
    @staticmethod
    def getClient() -> AsyncIOMotorClient:
        """Get the MongoDB client instance"""
        instance = DatabaseConnection()
        if not instance._initialized or not instance._client:
            raise RuntimeError("Database not initialized. Call initialize() first.")
        return instance._client
    
    @staticmethod
    def getDatabase() -> AsyncIOMotorDatabase:
        """Get the MongoDB database instance"""
        instance = DatabaseConnection()
        if not instance._initialized or not instance._database:
            raise RuntimeError("Database not initialized. Call initialize() first.")
        return instance._database
    
    @staticmethod
    async def close():
        """Close the database connection"""
        instance = DatabaseConnection()
        if instance._client:
            instance._client.close()
            instance._initialized = False
            logger.info("Database connection closed")


# Create global singleton instance
databaseConnection = DatabaseConnection()

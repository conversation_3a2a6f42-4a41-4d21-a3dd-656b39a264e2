"""Market Configuration for country-region mappings"""
import os
import yaml
import logging
from typing import Dict, Optional

logger = logging.getLogger(__name__)


class MarketConfig:
    """Market configuration for country-region mappings"""
    
    _country_region_map: Optional[Dict[str, str]] = None
    
    @classmethod
    def getCountryRegionMap(cls) -> Dict[str, str]:
        """Get country-region mapping, loading it if not already loaded"""
        if cls._country_region_map is None:
            cls._loadMarketData()
        return cls._country_region_map or {}
    
    @classmethod
    def getRegionByCountry(cls, countryCode: Optional[str]) -> Optional[str]:
        """Get region by country code"""
        if not countryCode:
            return "EMEA"  # Default fallback
        
        country_map = cls.getCountryRegionMap()
        region = country_map.get(countryCode.upper())
        
        if region:
            return region
        
        # Fallback to default logic if country not found in market data
        logger.warning(f"Country code '{countryCode}' not found in market data, using default region mapping")
        if countryCode.upper() in ['US', 'CA', 'MX']:
            return "NAFTA"
        return "EMEA"
    
    @classmethod
    def _loadMarketData(cls) -> None:
        """Load market data from YAML file and create country-to-region mapping"""
        try:
            # Get the path to the market.yml file
            current_dir = os.path.dirname(os.path.abspath(__file__))
            market_file_path = os.path.join(current_dir, 'market.yml')
            
            if not os.path.exists(market_file_path):
                logger.warning(f"Market file not found at {market_file_path}, using default mapping")
                cls._country_region_map = cls._getDefaultCountryRegionMap()
                return
            
            with open(market_file_path, 'r', encoding='utf-8') as file:
                market_data = yaml.safe_load(file)
            
            country_region_map = {}
            
            # Parse the YAML structure - it's a list of market entries
            if isinstance(market_data, list):
                for entry in market_data:
                    if isinstance(entry, dict) and 'I_COUNTRY' in entry and 'C_REGION' in entry:
                        country_code = entry['I_COUNTRY']
                        region = entry['C_REGION']
                        country_region_map[country_code] = region
            
            cls._country_region_map = country_region_map
            logger.info(f"Loaded {len(country_region_map)} country-region mappings from market.yml")
            
        except Exception as e:
            logger.error(f"Error loading market data: {str(e)}, using default mapping")
            cls._country_region_map = cls._getDefaultCountryRegionMap()
    
    @classmethod
    def _getDefaultCountryRegionMap(cls) -> Dict[str, str]:
        """Get default country-to-region mapping as fallback"""
        return {
            'US': 'NAFTA',
            'CA': 'NAFTA',
            'MX': 'NAFTA'
        }

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Simple configuration class to load environment variables"""
    
    # Symfony Framework Bundle
    APP_ENV = os.getenv("APP_ENV", "dev")
    APP_SECRET = os.getenv("APP_SECRET")
    APP_RUNTIME_ENV = os.getenv("APP_RUNTIME_ENV", "dev")
    
    # Microservice URLs
    MS_SYS_OMNI_URL = os.getenv("MS_SYS_OMNI_URL")
    MS_SYS_CORVET_DATA_URL = os.getenv("MS_SYS_CORVET_DATA_URL")
    MS_SYS_IDP_URL = os.getenv("MS_SYS_IDP_URL")
    MS_MYM1_SYS_USER_DATA_URL = os.getenv("MS_MYM1_SYS_USER_DATA_URL")
    MS_SYS_SDPR_URL = os.getenv("MS_SYS_SDPR_URL")
    MY_MARQUE_PROC_ME_URL = os.getenv("MY_MARQUE_PROC_ME_URL")
    MS_SYS_SAMS_DATA_URL = os.getenv("MS_SYS_SAMS_DATA_URL")
    MS_SYS_USER_DB_URL = os.getenv("MS_SYS_USER_DB_URL")
    SPACE_MIDDLEWARE_API_KEY = os.getenv("SPACE_MIDDLEWARE_API_KEY")
    SETTINGS_CDN_URL = os.getenv("SETTINGS_CDN_URL")
    
    # SQS parameters
    MESSENGER_TRANSPORT_DSN = os.getenv("MESSENGER_TRANSPORT_DSN")
    AWS_SQS_REFRESH_VEHICLES_SUBSCRIPTION_QUEUE_NAME = os.getenv("AWS_SQS_REFRESH_VEHICLES_SUBSCRIPTION_QUEUE_NAME")
    AWS_SQS_REFRESH_VEHICLES_NAME = os.getenv("AWS_SQS_REFRESH_VEHICLES_NAME")
    AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
    AWS_DEFAULT_REGION = os.getenv("AWS_DEFAULT_REGION")
    AWS_SQS_CONSUMER_RIGHTS_QUEUE = os.getenv("AWS_SQS_CONSUMER_RIGHTS_QUEUE")
    
    # E-Routes
    EROUTES_DEFAULT_ANDROID_LINK = os.getenv("EROUTES_DEFAULT_ANDROID_LINK")
    EROUTES_DEFAULT_IOS_LINK = os.getenv("EROUTES_DEFAULT_IOS_LINK")
    
    # MongoDB ODM Configuration
    MONGO_DB_URL = os.getenv("MONGO_DB_URL")
    MONGODB_DB = os.getenv("MONGODB_DB")
    REQUEST_TTL = int(os.getenv("REQUEST_TTL", "3600"))

# Create a global config instance
config = Config()

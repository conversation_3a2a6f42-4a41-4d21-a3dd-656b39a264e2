"""Feature Code Configuration"""
from typing import Dict, Any, Optional, List


class FeatureCodeConfig:
    """Feature code configuration constants"""
    
    BRAND_MAP = {
        'AC': {'name': 'Citroen', 'legacy': 'xp'},
        'AR': {'name': 'Alfa Romeo', 'legacy': 'xf'},
        'JE': {'name': 'Jeep', 'legacy': 'xf'},
        'OP': {'name': 'Opel', 'legacy': 'xp'},
        'FT': {'name': 'Fiat', 'legacy': 'xf'},
        'DS': {'name': 'DS', 'legacy': 'xp'},
        'LA': {'name': 'Lancia', 'legacy': 'xf'},
        'CY': {'name': '<PERSON><PERSON>', 'legacy': 'xf'},
        'DG': {'name': 'Dodge', 'legacy': 'xf'},
        'RM': {'name': 'RAM', 'legacy': 'xf'},
        'AP': {'name': 'Peugeot', 'legacy': 'xp'},
        'FO': {'name': 'Fiat Professional', 'legacy': 'xf'}
    }
    
    NAE01 = {
        'VEHICLE_INFO': {
            'code': 'VEHICLE_INFO',
            'status': 'enable',
            'value': 'NAE01',
            'backend': 'SpaceMid',
            'config': {
                'engine': 'UNKNOWN',
            },
        },
        'VEHICLE_HEALTH_ALERTS': {
            'code': 'VEHICLE_HEALTH_ALERTS',
            'status': 'enable',
            'value': 'NAE01',
            'backend': 'SpaceMid',
        },
        'VEHICLE_HEALTH_REPORT': {
            'code': 'VEHICLE_HEALTH_REPORT',
            'status': 'enable',
            'value': 'NAE01',
            'backend': 'SpaceMid',
        },
    }
    
    NAK01 = {
        'SEND_TO_NAV': {
            'code': 'SEND_TO_NAV',
            'status': 'enable',
            'value': 'NAK01',
            'config': {
                'protocol': 'network | ble'
            },
        },
    }
    
    EV_TRIP_PLANNER = {
        'EV_TRIP_PLANNER': {
            'code': 'EV_TRIP_PLANNER',
            'status': 'enable',
            'value': 'NAL01 OR NAL03',
            'backend': 'SpaceMid',
            'config': {
                'version': '',
            },
        },
    }
    
    DYNAMIC_RANGE_MAP = {
        'DYNAMIC_RANGE_MAP': {
            'code': 'DYNAMIC_RANGE_MAP',
            'status': 'enable',
            'value': 'NAL01 OR NAL03',
            'backend': 'SpaceMid',
            'config': {
                'version': '',
            },
        },
    }
    
    NAM01 = {
        'DIGITAL_KEY': {
            'code': 'DIGITAL_KEY',
            'status': 'enable',
            'value': 'NAM01',
            'backend': 'SpaceMid',
            'config': {
                'type': 'NEA | Atlantis | Brain',
            },
        },
    }
    
    NAO01 = {
        'VEHICLE_INFO': {
            'code': 'VEHICLE_INFO',
            'status': 'enable',
            'value': 'NAO01',
            'backend': 'APIM',
            'config': {
                'engine': 'BEV',
            },
        },
    }
    
    NAO02 = {
        'VEHICLE_INFO': {
            'code': 'VEHICLE_INFO',
            'status': 'enable',
            'value': 'NAO02',
            'backend': 'APIM',
            'config': {
                'engine': 'PHEV',
            },
        },
    }
    
    NAS01 = {
        'CLIMATE_SCHEDULING': {
            'code': 'CLIMATE_SCHEDULING',
            'status': 'enable',
            'value': 'NAS01',
            'backend': 'APIM',
            'config': {
                'schedule': 4,
                'repeatBehaviour': 'repeat',
                'shared': False,
                'daysType': ['chooseDays'],
                'editable': {
                    'temperature': {
                        'enable': False,
                        'unit': 'celsius',
                        'default': 21,
                    },
                },
            },
        },
        "PRECONDIONNING_ON": {
            "code": "PRECONDIONNING_ON",
            "status": "enable",
            "value": "NAS01",
            "backend": "SpaceMid",
        },
        "PRECONDIONNING_OFF": {
            "code": "PRECONDIONNING_OFF",
            "status": "enable",
            "value": "NAS01",
            "backend": "SpaceMid",
        },
        "AIR_CONDITIONNING_ON": {
            "code": "AIR_CONDITIONNING_ON",
            "status": "enable",
            "value": "NAS01",
            "backend": "SpaceMid",
        },
        "AIR_CONDITIONNING_OFF": {
            "code": "AIR_CONDITIONNING_OFF",
            "status": "enable",
            "value": "NAS01",
            "backend": "SpaceMid",
        }
    }
    
    NAS02 = {
        'CLIMATE_SCHEDULING': {
            'code': 'CLIMATE_SCHEDULING',
            'status': 'enable',
            'value': 'NAS02',
            'backend': 'APIM',
            'config': {
                'schedule': 4,
                'repeatBehaviour': 'repeat',
                'shared': False,
                'daysType': ['chooseDays'],
                'editable': {
                    'temperature': {
                        'enable': False,
                        'unit': 'celsius',
                        'default': 21,
                    },
                },
            },
        },
    }
    
    NAU01 = {
        'E_ROUTES': {
            'code': 'E_ROUTES',
            'status': 'enable',
            'value': 'NAU01',
            'backend': 'SpaceMid',
            'config': {
                'linkAndroid': '',
                'linkIos': '',
            },
        },
    }
    
    NAW01 = {
        'TRIPS': {
            'code': 'TRIPS',
            'status': 'enable',
            'value': 'NAW01',
            'backend': 'APIM',
            'config': {
                'protocol': 'network | ble',
            },
        },
        'VEHICLE_LOCATOR': {
            'code': 'VEHICLE_LOCATOR',
            'status': 'enable',
            'value': 'NAW01',
            'backend': 'APIM',
            'config': {
                'location': ['trip', 'manual', 'remote'],
                'refresh': False,
            },
        },
    }
    
    NON_NAW01 = {
        'VEHICLE_LOCATOR': {
            'code': 'VEHICLE_LOCATOR',
            'status': 'enable',
            'value': '',
            'backend': 'APIM',
            'config': {
                'location': ['manual'],
                'refresh': False,
            },
        },
    }
    
    NBM01 = {
        'CHARGE_NOW_START': {
            'code': 'CHARGE_NOW_START',
            'status': 'enable',
            'value': 'NBM01',
            'backend': 'APIM',
        },
        'CHARGE_DEFERRED_START': {
            'code': 'CHARGE_DEFERRED_START',
            'status': 'enable',
            'value': 'NBM01',
            'backend': 'APIM',
        },
    }
    
    NBM02 = {
        'CHARGE_NOW_START': {
            'code': 'CHARGE_NOW_START',
            'status': 'enable',
            'value': 'NBM02',
            'backend': 'APIM',
        },
        'CHARGE_SCHEDULING': {
            'code': 'CHARGE_SCHEDULING',
            'status': 'enable',
            'value': 'NBM02',
            'backend': 'APIM',
            'config': {
                'schedule': 1,
                'repeatBehaviour': 'once',
                'shared': False,
                'daysType': ['daily'],
            },
        },
        'CHARGE_DEFERRED_START': {
            'code': 'CHARGE_DEFERRED_START',
            'status': 'enable',
            'value': 'NBM02',
            'backend': 'APIM',
        },
    }
    
    NBI01 = {
        'CHARGE_DEFERRED_STOP': {
            'code': 'CHARGE_DEFERRED_STOP',
            'status': 'enable',
            'value': 'NBI01',
        },
    }
    
    NBG01 = {
        'CHARGE_LIMIT': {
            'code': 'CHARGE_LIMIT',
            'status': 'enable',
            'value': 'NBG01',
            'backend': 'APIM',
            'config': {
                'version': 1,
                'limits': ['regular', 'full'],
            },
        },
    }
    
    NCG01 = {
        'BATTERY_USAGE_TIPS': {
            'code': 'BATTERY_USAGE_TIPS',
            'status': 'enable',
            'value': 'NCG01',
        },
    }
    
    NEE02 = {
        'DOOR_LOCK': {
            'code': 'DOOR_LOCK',
            'status': 'enable',
            'value': 'NEE02',
            'backend': 'APIM',
            'config': {
                'version': 1,
            },
        },
        'DOOR_UNLOCK': {
            'code': 'DOOR_UNLOCK',
            'status': 'enable',
            'value': 'NEE02',
            'backend': 'APIM',
            'config': {
                'version': 1,
            },
        },
        'VEHICLE_OPENINGS_INFO': {
            'code': 'VEHICLE_OPENINGS_INFO',
            'status': 'enable',
            'value': 'NEE02',
            'backend': 'APIM',
        },
    }
    
    NEF01 = {
        'HORN': {
            'code': 'HORN',
            'status': 'enable',
            'value': 'NEF01',
            'backend': 'APIM',
            'config': {
                'version': 1,
            },
        },
        'LIGHTS': {
            'code': 'LIGHTS',
            'status': 'enable',
            'value': 'NEF01',
            'backend': 'APIM',
            'config': {
                'version': 1,
            },
        },
    }
    
    NFC01 = {
        'CONNECTED_ALARM_STATUS': {
            'code': 'CONNECTED_ALARM_STATUS',
            'status': 'enable',
            'value': 'NFC01',
            'backend': 'APIM',
        },
    }
    
    NFD01 = {
        'CONNECTED_ALARM': {
            'code': 'CONNECTED_ALARM',
            'status': 'enable',
            'value': 'NFD01',
            'backend': 'APIM',
            'config': {
                'suppression': 'disable',
            },
        },
    }
    
    SMARTPHONE_STATION = {
        'SMARTPHONE_STATION': {
            'code': 'SMARTPHONE_STATION',
            'status': 'enable',
            'value': '',
            "backend": "SpaceMid",
            'config': {
                "vehicleCode": "",
                'type': "",
                "eligibilityDisclaimer": ""
            },
        },
    }
    
    ADD_VEHICLE = {
        'GAS_STATION_LOCATOR': {
            'code': 'GAS_STATION_LOCATOR',
            'status': 'enable',
            'value': '',
            'backend': 'SpaceMid',
            'config': {
                'partner': 'google',
            },
        },
        'HYDROGEN_STATION_LOCATOR': {
            'code': 'HYDROGEN_STATION_LOCATOR',
            'status': 'enable',
            'value': '',
            'backend': 'SpaceMid',
            'config': {
                'partner': 'tomtom',
            },
        },
        'CHARGE_STATION_LOCATOR': {
            'code': 'CHARGE_STATION_LOCATOR',
            'status': 'enable',
            'value': '',
            'backend': 'SpaceMid',
            # 'config': {
            #     'type': 'partner',
            #     'url': 'free2moveeu://',
            #     'appName': 'eSolution Charging',
            #     'packageName': 'com.f2m.esolutions.esolutions',
            #     'appId': '**********',
            # },
        },
        'CHARGING_STATION_MANAGEMENT': {
            'code': 'CHARGING_STATION_MANAGEMENT',
            'status': 'enable',
            'value': '',
            'backend': 'SpaceMid',
            'config': {
                'partner': 'f2mc',
                'enrolmentStatus': 'noAccountLinking',
            },
        },
    }
    
    VEHICLE_DEEP_REFRESH = {
        'code': 'VEHICLE_DEEP_REFRESH',
        'status': 'enable',
        'value': '',
    }
    
    NAB01 = {
        'UBI_PHYD': {
            'code': 'UBI_PHYD',
            'status': 'enable',
            'value': 'NAB01',
            'backend': 'SpaceMid',
        },
    }
    
    @classmethod
    def getFeatureConfig(cls, name: str) -> Optional[Dict[str, Any]]:
        """Get a specific feature config by name"""
        return getattr(cls, name, None)
    
    @classmethod
    def getAllFeatureConfig(cls) -> Dict[str, Any]:
        """Get all feature configs"""
        result = {}
        for attr_name in dir(cls):
            if not attr_name.startswith('_') and attr_name.isupper():
                result[attr_name] = getattr(cls, attr_name)
        return result
    
    @classmethod
    def getAddVehicleFeatures(cls, featureKey: Optional[str] = None) -> Any:
        """Get ADD_VEHICLE feature configurations"""
        if featureKey:
            return cls.ADD_VEHICLE.get(featureKey)
        return cls.ADD_VEHICLE

import logging
from typing import Dict, Any, Optional ,List
from app.model.userData import UserData
from app.model.vehicle_model import Vehicle
from beanie import PydanticObjectId
from beanie.operators import In, And
logger = logging.getLogger(__name__)

class VehicleService:
    """Singleton service for vehicle-related operations"""
    
    _instance: Optional['VehicleService'] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @staticmethod
    async def getVehicleFromUserData(
        userData: UserData,
        criteriaValue: str,
        criteriaKey: str = "vin"
    ) -> Optional[object]:
        """Find a vehicle in user data based on criteria key and value"""
        for vehicle in userData.vehicle:
            # Get the attribute value from the vehicle object
            vehicleAttrValue = getattr(vehicle, criteriaKey, None)
            if vehicleAttrValue == criteriaValue:
                return vehicle
        return None
    
    @staticmethod
    async def findVehicleByVin(userData: UserData, vin: str) -> Optional[object]:
        """Find a vehicle by VIN"""
        return await VehicleService.getVehicleFromUserData(userData, vin, "vin")
    
    @staticmethod
    async def findVehicleById(userData: UserData, vehicleId: str) -> Optional[object]:
        """Find a vehicle by ID"""
        return await VehicleService.getVehicleFromUserData(userData, vehicleId, "id")

    async def get_user_vehicles(
        self,
        user_id: str,
        brand: str,
        language: str,
        country: str
    ) -> List[Vehicle]:
        """
        Get vehicles for a specific user filtered by brand, language, and country
        
        Args:
            user_id: User identifier
            brand: Vehicle brand
            language: Language code
            country: Country code
            
        Returns:
            List of Vehicle documents
        """
        try:
            logger.info(f"Fetching vehicles for user {user_id} with brand {brand}")
            
            # Build query filters
            query_filters = {
                "user_id": user_id,
                "brand": brand,
                "country": country,
                "language": language
            }
            
            # Execute query
            vehicles = await Vehicle.find(query_filters).to_list()
            
            logger.info(f"Found {len(vehicles)} vehicles for user {user_id}")
            
            return vehicles
            
        except Exception as e:
            logger.error(f"Error fetching vehicles from database: {e}", exc_info=True)

            # If database is not initialized, return mock data for testing
            if "CollectionWasNotInitialized" in str(e):
                logger.warning("Database not initialized, returning mock vehicle data")
                return self._get_mock_vehicles(user_id, brand)

            raise

    def _get_mock_vehicles(self, user_id: str, brand: str) -> List[dict]:
        """Return mock vehicle data when database is not available"""
        return [
            {
                "id": "mock_vehicle_1",
                "vin": "WVWZZZ1JZ3W386752",
                "lcdv": "MOCK_LCDV_001",
                "visual": "mock_visual_1.jpg",
                "short_label": f"Mock {brand} Vehicle 1",
                "nickname": "My Test Car",
                "warranty_start_date": 1640995200,  # 2022-01-01
                "command": "mock_command",
                "sdp": "mock_sdp_1",
                "user_id": user_id,
                "brand": brand
            },
            {
                "id": "mock_vehicle_2",
                "vin": "WVWZZZ1JZ3W386753",
                "lcdv": "MOCK_LCDV_002",
                "visual": "mock_visual_2.jpg",
                "short_label": f"Mock {brand} Vehicle 2",
                "nickname": "Test Vehicle 2",
                "warranty_start_date": 1672531200,  # 2023-01-01
                "command": "mock_command_2",
                "sdp": "mock_sdp_2",
                "user_id": user_id,
                "brand": brand
            }
        ]
    
    async def get_vehicle_by_vin(self, vin: str, user_id: str) -> Optional[Vehicle]:
        """
        Get a specific vehicle by VIN and user ID
        
        Args:
            vin: Vehicle Identification Number
            user_id: User identifier
            
        Returns:
            Vehicle document or None if not found
        """
        try:
            vehicle = await Vehicle.find_one({
                "vin": vin,
                "user_id": user_id
            })
            
            return vehicle
            
        except Exception as e:
            logger.error(f"Error fetching vehicle by VIN {vin}: {e}", exc_info=True)
            raise
    
    async def get_vehicle_by_id(self, vehicle_id: str, user_id: str) -> Optional[Vehicle]:
        """
        Get a specific vehicle by ID and user ID
        
        Args:
            vehicle_id: Vehicle document ID
            user_id: User identifier
            
        Returns:
            Vehicle document or None if not found
        """
        try:
            vehicle = await Vehicle.find_one({
                "_id": PydanticObjectId(vehicle_id),
                "user_id": user_id
            })
            
            return vehicle
            
        except Exception as e:
            logger.error(f"Error fetching vehicle by ID {vehicle_id}: {e}", exc_info=True)
            raise


# Create global singleton instance
vehicleService = VehicleService()
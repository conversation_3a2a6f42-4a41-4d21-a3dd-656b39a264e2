from app.connector.sysIdpConnector import sysIdpConnector
from app.helper.response import WSResponse
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class ConsumerRightsService:
    """Singleton service for consumer rights operations"""
    
    _instance: Optional['ConsumerRightsService'] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @staticmethod
    async def fetchConsumerRights(params: Dict[str, Any]) -> WSResponse:
        try:
            options = {
                "params": {
                    "userDbId": params.get("userDbId"),
                    "vin": params.get("vin"),
                },
            }
            logger.info(f"ConsumerRightsService.fetchConsumerRights {options}")
            return await sysIdpConnector.call("GET", "/v1/consumer-rights", options)
        except Exception as e:
            logger.error(f"ConsumerRightsService.fetchConsumerRights : Caught Exception {str(e)}")
            return WSResponse(getattr(e, "code", 500), str(e))


# Create global singleton instance
consumerRightsService = ConsumerRightsService()
import logging
from typing import Dict, Any, Optional, List
from app.helper.response import WSResponse
from app.connector.sysSamsDataConnector import sysSamsDataConnector

logger = logging.getLogger(__name__)


class SubscriptionService:
    """Singleton service for subscription-related operations"""
    
    _instance: Optional['SubscriptionService'] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @staticmethod
    async def fetchSubscriptionData(params: Dict[str, Any], target: str) -> WSResponse:
        """Fetch subscription data from external service"""
        try:
            options = {
                "params": {
                    'target': target
                },
                "headers": {
                    "userId": params.get("userDbId"),
                    "vin": params.get("vin"),
                },
            }
            logger.info(f"SubscriptionService.fetchSubscriptionData {options}")
            return await sysSamsDataConnector.call("GET", "/v1/subscription", options)
        except Exception as e:
            logger.error(f"SubscriptionService.fetchSubscriptionData : Caught Exception {str(e)}")
            return WSResponse(getattr(e, "code", 500), str(e))


# Create global singleton instance
subscriptionService = SubscriptionService()

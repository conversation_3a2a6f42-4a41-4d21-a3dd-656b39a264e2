from typing import Optional, List, Dict, Any
from app.model.userData import UserData


class UserDataService:
    """Singleton service for user data operations"""
    
    _instance: Optional['UserDataService'] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @staticmethod
    async def getUserData(userId: str) -> Optional[UserData]:
        """Get user data by user ID"""
        return await UserData.find_one(UserData.userId == userId)
    
    @staticmethod
    async def createUserData(userData: Dict[str, Any]) -> UserData:
        """Create new user data"""
        newUserData = UserData(**userData)
        await newUserData.insert()
        return newUserData
    
    @staticmethod
    async def updateUserData(userId: str, updateData: Dict[str, Any]) -> bool:
        """Update existing user data"""
        userData = await UserDataService.getUserData(userId)
        if userData:
            for key, value in updateData.items():
                setattr(userData, key, value)
            await userData.save()
            return True
        return False
    
    @staticmethod
    async def deleteUserData(userId: str) -> bool:
        """Delete user data"""
        userData = await UserDataService.getUserData(userId)
        if userData:
            await userData.delete()
            return True
        return False
    
    @staticmethod
    async def getUserVehicles(userId: str) -> List[Any]:
        """Get all vehicles for a user"""
        userData = await UserDataService.getUserData(userId)
        if userData and hasattr(userData, 'vehicle'):
            return userData.vehicle
        return []


# Create global singleton instance
userDataService = UserDataService()
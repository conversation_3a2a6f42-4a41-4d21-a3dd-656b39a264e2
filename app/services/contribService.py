import logging
from typing import Dict, Any, Optional
from fastapi import HTT<PERSON>Ex<PERSON>, status
from app.helper.response import WSResponse
from app.schemas.vehicleSchemas import <PERSON>rrorD<PERSON>il, ErrorResponse
from app.connector.sysSamsDataConnector import sysSamsDataConnector

logger = logging.getLogger(__name__)


class ContribService:
    """Singleton service for contribution data operations"""
    
    _instance: Optional['ContribService'] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @staticmethod
    async def getContribInfoByProductIds(
        productIds: str,
        brand: str,
        culture: str,
        source: Optional[str] = "APP"
    ) -> Dict[str, Any]:
        """
        Get contribution data by multiple product IDs
        
        Args:
            productIds: Comma-separated product identifiers
            brand: Brand identifier
            culture: Culture/language code
            source: Source identifier (default: "APP")
            
        Returns:
            Contribution data or raises HTTPException
        """
        try:
            options = {
                "params": {
                    "brand": brand,
                    "culture": culture,
                    "source": source,
                    "productIds": productIds
                }
            }
            
            uri = "/v1/sams/contrib/getInfoByProduct"
            logger.info(f"ContribService.getContribInfoByProductIds => Call API [{uri}] with options {options}")
            
            response = await sysSamsDataConnector.call("GET", uri, options)
            
            if response.statusCode != status.HTTP_200_OK:
                data = response.data
                if isinstance(data, dict) and "error" in data:
                    errorData = data["error"]
                    if isinstance(errorData, dict):
                        raise HTTPException(
                            status_code=response.statusCode,
                            detail=ErrorDetail(**errorData),
                        )
                    else:
                        raise HTTPException(
                            status_code=response.statusCode,
                            detail=ErrorDetail(message=str(errorData)),
                        )
                raise HTTPException(
                    status_code=response.statusCode,
                    detail=ErrorDetail(message=data if isinstance(data, str) else str(data)),
                )
            
            # Extract success data
            contribData = response.data.get("success", {}) if isinstance(response.data, dict) else response.data
            return contribData
            
        except HTTPException:
            # Re-raise HTTPException to let FastAPI handle it
            raise
        except Exception as e:
            logger.error(f"ContribService.getContribInfoByProductIds : Caught Exception {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ErrorDetail(message=str(e)),
            )
    
    @staticmethod
    async def getContribByVin(vin: str, brand: str, culture: str) -> Dict[str, Any]:
        """Get contribution data by VIN"""
        # Implementation to be added based on business logic
        return {}


# Create global singleton instance
contribService = ContribService()
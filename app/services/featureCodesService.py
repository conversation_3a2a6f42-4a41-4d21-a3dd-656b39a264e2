"""Feature Codes Service for calculating vehicle feature codes"""
import logging
import os
import re
import aiohttp
from typing import Dict, Any, List, Optional
from app.helper.response import WSResponse
from app.schemas.featureCode import FeatureCodeConstants, FeatureCode
from app.config.featureCodeConfig import FeatureCodeConfig
from app.helper.vehicleTypeEntities import ENGINE_TYPES
from app.services.consumerRightsService import consumerRightsService
from app.config.marketConfig import MarketConfig
from app.repository.spsEligibilityRepository import SPSEligibilityRepository

logger = logging.getLogger(__name__)


class FeatureCodesService:
    """Singleton service for feature code operations"""
    
    _instance: Optional['FeatureCodesService'] = None
    _cdnUrl: Optional[str] = None
    _httpClient = None  # Will be initialized when needed
    _spsEligibilityRepository = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            from app.config.config import config
            cls._cdnUrl = config.SETTINGS_CDN_URL
            cls._spsEligibilityRepository = SPSEligibilityRepository()
        return cls._instance
    
    
    @staticmethod
    async def getServiceCodesByVin(vin: str, userDbId: str) -> List[str]:
        """
        Retrieves the service codes associated with a specific VIN for a given user,
        from the CVS consumer rights API.
        
        Args:
            vin: The Vehicle Identification Number (VIN) to search for
            userDbId: The user database ID for whom the data is retrieved
            
        Returns:
            List of service codes associated with the given VIN
        """
        try:
            consumerRightsData = await consumerRightsService.fetchConsumerRights({
                "userDbId": userDbId,
                "vin": vin
            })
            if consumerRightsData.statusCode != 200:
                logger.warning(f"Failed to get consumer rights data: {consumerRightsData.data}")
                return []
            
            codes = []
            data = consumerRightsData.data
            services = data.get("success", {}).get("services", {})
            
            # Iterate through each service key (e.g., '00000000000000000096', 'A8113564478677467970', etc.)
            for serviceKey, serviceArray in services.items():
                # Each serviceArray contains multiple objects
                for serviceObj in serviceArray:
                    code = serviceObj.get("code")
                    if code:
                        codes.append(code)
            
            logger.info(f"Service codes retrieved for VIN {vin}: {', '.join(codes)}")
            return codes
            
        except Exception as e:
            logger.error(f"Error in getServiceCodesByVin: {str(e)}")
            return []
    
    @staticmethod
    async def getFeaturesCode(
        params: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        logger.info(f"Starting getFeaturesCode for userId: {params['userId']}, vin: {params['vin']}, lcdv: {params['lcdv']}")
        
        try:
            # Load feature code config
            featureCodeConfig = FeatureCodeConfig.getAllFeatureConfig()
            
            # Call the CVS consumer rights API to get the Service codes or FDS
            serviceCodes = []
            if params.get('userDbId'):
                try:
                    serviceCodes = await self.getServiceCodesByVin(params['vin'], params['userDbId'])
                except Exception as e:
                    serviceCodes = []
                    logger.warning(f"Failed to get service codes, continuing with non-FDS features: {str(e)}")
            
            # Add NAE01 service code for XF brand
            legacy = FeatureCodeConfig.BRAND_MAP.get(params['brand'].upper(), {}).get('legacy', 'xf')
            if legacy == 'xf' and 'NAE01' not in serviceCodes:
                serviceCodes.append('NAE01')
            isXfLegacy = True if legacy == 'xf' else False
            
            logger.info(f"Service codes retrieved: {serviceCodes}")
            
            featureCodes = await self.dynamicStatusConfigCalculation(
                featureCodeConfig,
                serviceCodes,
                params['brand'],
                params['lcdv'],
                params['type'],
                params['corvetAttributes'],
                params['f2mcObj'],
                params['country'],
                params['includeNonFds'],
                isXfLegacy
            )
            
            logger.info(f"Calculated feature codes: {[fc.get('code') for fc in featureCodes]}")
            return featureCodes
            
        except Exception as e:
            logger.error(f"Error in getFeaturesCode: {str(e)}")
            return []
    
    @staticmethod
    async def dynamicStatusConfigCalculation(
        featureCodeConfig: Dict[str, Any],
        serviceCodes: List[str],
        brand: str,
        lcdv: str,
        engineType: str,
        corvetAttributes: List[str],
        f2mcObj: Optional[Dict[str, Any]] = None,
        country: Optional[str] = None,
        includeNonFds: bool = True,
        isXfLegacy: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Calculates and assigns feature codes based on various conditions.
        """
        try:
            je_brand = "JE" if lcdv and lcdv[:4] == "1JJP" else None
            
            featureCodes = []
            bypass_service_keys = ['NAL01', 'NAL03']  # EV Trip Planner / EV Routing
            
            for serviceKey in serviceCodes:
                if serviceKey in featureCodeConfig or serviceKey in bypass_service_keys:
                    await self._processServiceCode(
                        serviceKey,
                        featureCodeConfig,
                        featureCodes,
                        brand,
                        isXfLegacy,
                        engineType,
                        lcdv,
                        corvetAttributes,
                        country
                    )
            
            # Add special features
            # SmartPhone Station eligibility
            spsFeature = await self.spsEligibility(lcdv, corvetAttributes)
            if spsFeature:
                featureCodes.append(spsFeature)
            
            # Find My Car feature
            findMyCarFeature = self.getFindMyCarFeature(featureCodeConfig, serviceCodes)
            if findMyCarFeature:
                featureCodes.append(findMyCarFeature)
            
            # Deep Refresh feature
            deepRefreshFeature = self.getDeepRefreshFeature(featureCodeConfig, serviceCodes)
            if deepRefreshFeature:
                featureCodes.append(deepRefreshFeature)
            
            # Non-FDS features
            if includeNonFds:
                # Gas Station Locator
                gasStationFeature = self.getGasStationLocatorFeature(engineType, je_brand)
                if gasStationFeature:
                    featureCodes.append(gasStationFeature)
                
                # Hydrogen Station Locator
                hydrogenStationFeature = self.getHydrogenStationLocatorFeature(engineType, je_brand)
                if hydrogenStationFeature:
                    featureCodes.append(hydrogenStationFeature)
                
                # Charging Station Locator
                chargingStationFeature = self.getChargingStationLocatorFeature(engineType, lcdv)
                if chargingStationFeature:
                    featureCodes.append(chargingStationFeature)
                
                # Charging Station Management
                chargingStationMgmtFeature = self.getChargingStationManagementFeature(
                    engineType, f2mcObj, country
                )
                if chargingStationMgmtFeature:
                    featureCodes.append(chargingStationMgmtFeature)
            
            return featureCodes
            
        except Exception as e:
            logger.error(f"Error in dynamicStatusConfigCalculation: {str(e)}")
            return []
    
    @staticmethod
    async def _processServiceCode(
        serviceKey: str,
        featureCodeConfig: Dict[str, Any],
        featureCodes: List[Dict[str, Any]],
        brand: Optional[str],
        isXfLegacy: bool,
        engineType: str,
        lcdv: str,
        corvetAttributes: List[str],
        country: Optional[str]
    ):
        """Process individual service code using optimized dispatch pattern"""
        
        # Create processing context for cleaner parameter passing
        context = {
            'brand': brand,
            'isXfLegacy': isXfLegacy,
            'engineType': engineType,
            'lcdv': lcdv,
            'corvetAttributes': corvetAttributes,
            'country': country,
            'region': MarketConfig.getRegionByCountry(country)
        }
        
        # Define service code processors
        processors = {
            'NAE01': self._process_NAE01,
            'NAK01': self._process_NAK01,
            'NAL01': self._process_NAL,
            'NAL03': self._process_NAL,
            'NAM01': self._process_NAM01,
            'NAO01': self._process_NAO,
            'NAO02': self._process_NAO,
            'NAS01': self._process_NAS01,
            'NAS02': self._process_NAS02,
            'NAU01': self._process_NAU01,
            'NAW01': self._process_NAW01,
            'NBM01': self._process_NBM01,
            'NBM02': self._process_NBM02,
            'NBI01': self._process_NBI01,
            'NBG01': self._process_NBG01,
            'NCG01': self._process_NCG01,
            'NEE02': self._process_NEE02,
            'NEF01': self._process_NEF01,
            'NFC01': self._process_NFC01,
            'NFD01': self._process_NFD01,
            'NAB01': self._process_NAB01,
        }
        
        # Execute the appropriate processor
        processor = processors.get(serviceKey)
        if processor:
            if serviceKey == 'NAU01':  # Special case for async method
                await processor(featureCodeConfig, featureCodes, serviceKey, context)
            else:
                processor(featureCodeConfig, featureCodes, serviceKey, context)
    
    def _process_NAE01(self, config: Dict, features: List, key: str, ctx: Dict):
        """Vehicle Health Report"""
        if ctx['brand'] == FeatureCodeConstants.BRAND_JEEP or ctx['isXfLegacy']:
            for feature in config[key].values():
                feature = feature.copy()
                if feature['code'] == 'VEHICLE_INFO':
                    feature['config']['engine'] = ENGINE_TYPES.get(ctx['engineType'], 'UNKNOWN')
                features.append(feature)
    
    def _process_NAK01(self, config: Dict, features: List, key: str, ctx: Dict):
        """Connected Navigation"""
        if ctx['brand'] == FeatureCodeConstants.BRAND_JEEP:
            feature = next(iter(config[key].values())).copy()
            feature['config']['protocol'] = FeatureCodeConstants.J4U_NAV_PROTOCOL
            features.append(feature)
        
        if re.match(r'^1I4[QTUVW]', ctx['lcdv']):
            feature = next(iter(config[key].values())).copy()
            feature['config']['protocol'] = FeatureCodeConstants.FIAT_NAV_PROTOCOL
            features.append(feature)
    
    def _process_NAL(self, config: Dict, features: List, key: str, ctx: Dict):
        """EV Trip Planner / Dynamic Range Map"""
        if ctx['engineType'] == FeatureCodeConstants.BATTERY_ELECTRIC_VEHICLE:
            if self.isAllowedLcdv(ctx['lcdv']):
                codes = [fc['code'] for fc in features]
                if 'EV_TRIP_PLANNER' not in codes:
                    feature = next(iter(config['EV_TRIP_PLANNER'].values())).copy()
                    feature['value'] = key
                    features.append(feature)
        
        if self.isAllowedLcdv(ctx['lcdv']):
            codes = [fc['code'] for fc in features]
            if 'DYNAMIC_RANGE_MAP' not in codes:
                feature = next(iter(config['DYNAMIC_RANGE_MAP'].values())).copy()
                feature['value'] = key
                feature['config']['type'] = ctx['engineType']
                features.append(feature)
    
    def _process_NAM01(self, config: Dict, features: List, key: str, ctx: Dict):
        """Digital Key"""
        if ctx['brand'] == FeatureCodeConstants.BRAND_JEEP:
            feature = next(iter(config[key].values())).copy()
            feature['config']['type'] = FeatureCodeConstants.J4U_DIGITAL_KEY_TYPE
            features.append(feature)
        elif ctx['brand'] == FeatureCodeConstants.BRAND_ALFA:
            feature = next(iter(config[key].values())).copy()
            feature['config']['type'] = FeatureCodeConstants.A5U_DIGITAL_KEY
            features.append(feature)
    
    def _process_NAO(self, config: Dict, features: List, key: str, ctx: Dict):
        """Remote LEV"""
        feature = next(iter(config[key].values())).copy()
        features.append(feature)
    
    def _process_NAS01(self, config: Dict, features: List, key: str, ctx: Dict):
        """Climate control"""
        if ctx['brand'] == FeatureCodeConstants.BRAND_JEEP:
            feature = config[key]['CLIMATE_SCHEDULING'].copy()
            features.append(feature)
        
        if 'DRE24CD' in ctx['corvetAttributes']:
            for subkey in ['PRECONDIONNING_ON', 'PRECONDIONNING_OFF', 
                          'AIR_CONDITIONNING_ON', 'AIR_CONDITIONNING_OFF']:
                if subkey in config[key]:
                    features.append(config[key][subkey].copy())
    
    def _process_NAS02(self, config: Dict, features: List, key: str, ctx: Dict):
        """Climate scheduling without immediate start"""
        if ctx['brand'] == FeatureCodeConstants.BRAND_JEEP:
            feature = next(iter(config[key].values())).copy()
            features.append(feature)
    
    @staticmethod
    async def _process_NAU01(config: Dict, features: List, key: str, ctx: Dict):
        """e-Routes"""
        await FeatureCodesService._processERoutes(key, config, features)
    
    def _process_NAW01(self, config: Dict, features: List, key: str, ctx: Dict):
        """Trips In Cloud"""
        self._processTrips(key, config, features, ctx['brand'], ctx['lcdv'])
    
    def _process_NBM01(self, config: Dict, features: List, key: str, ctx: Dict):
        """Charge control"""
        for feature in config[key].values():
            features.append(feature.copy())
    
    def _process_NBM02(self, config: Dict, features: List, key: str, ctx: Dict):
        """Charge scheduling"""
        if ctx['region'] == FeatureCodeConstants.REGION_NAFTA:
            for feature in config[key].values():
                features.append(feature.copy())
    
    def _process_NBI01(self, config: Dict, features: List, key: str, ctx: Dict):
        """Charge deferred stop"""
        if ctx['region'] == FeatureCodeConstants.REGION_NAFTA:
            feature = next(iter(config[key].values())).copy()
            features.append(feature)
    
    def _process_NBG01(self, config: Dict, features: List, key: str, ctx: Dict):
        """Charge till 80%"""
        if ctx['brand'] == FeatureCodeConstants.BRAND_JEEP:
            feature = next(iter(config[key].values())).copy()
            features.append(feature)
    
    def _process_NCG01(self, config: Dict, features: List, key: str, ctx: Dict):
        """Battery usage tips"""
        feature = next(iter(config[key].values())).copy()
        features.append(feature)
    
    def _process_NEE02(self, config: Dict, features: List, key: str, ctx: Dict):
        """Remote Locking"""
        for feature in config[key].values():
            features.append(feature.copy())
    
    def _process_NEF01(self, config: Dict, features: List, key: str, ctx: Dict):
        """Remote Vehicle Tracking"""
        for feature in config[key].values():
            features.append(feature.copy())
    
    def _process_NFC01(self, config: Dict, features: List, key: str, ctx: Dict):
        """Alarm Status"""
        feature = next(iter(config[key].values())).copy()
        features.append(feature)
    
    def _process_NFD01(self, config: Dict, features: List, key: str, ctx: Dict):
        """Alarm Alert"""
        if ctx['brand'] == FeatureCodeConstants.BRAND_JEEP or re.match(r'^1I4[QTUVW]', ctx['lcdv']):
            feature = next(iter(config[key].values())).copy()
            features.append(feature)
    
    def _process_NAB01(self, config: Dict, features: List, key: str, ctx: Dict):
        """UBI - Pay How You Drive"""
        if ctx['region'] == FeatureCodeConstants.REGION_NAFTA:
            feature = next(iter(config[key].values())).copy()
            features.append(feature)
    
    @staticmethod
    async def _processERoutes(
        serviceKey: str,
        featureCodeConfig: Dict[str, Any],
        featureCodes: List[Dict[str, Any]]
    ):
        """Process e-Routes feature"""
        try:
            if FeatureCodesService._cdnUrl:
                if not FeatureCodesService._httpClient:
                    FeatureCodesService._httpClient = aiohttp.ClientSession()
                
                async with FeatureCodesService._httpClient.get(FeatureCodesService._cdnUrl) as response:
                    if response.status == 200:
                        data = await response.json()
                        if 'eROUTES' in data:
                            feature = next(iter(featureCodeConfig[serviceKey].values())).copy()
                            feature['config']['linkAndroid'] = data['eROUTES'].get('linkAndroid', '')
                            feature['config']['linkIos'] = data['eROUTES'].get('linkIos', '')
                            featureCodes.append(feature)
                        else:
                            logger.error("eROUTES key is missing in CDN response")
                    else:
                        logger.error(f"Failed to fetch data from CDN. Status code: {response.status}")
        except Exception as e:
            logger.error(f"Error in NAU01 case: {str(e)}")
    
    def _processTrips(
        self,
        serviceKey: str,
        featureCodeConfig: Dict[str, Any],
        featureCodes: List[Dict[str, Any]],
        je_brand: Optional[str],
        lcdv: str
    ):
        """Process trips feature"""
        if je_brand == FeatureCodeConstants.BRAND_JEEP:
            for feature in featureCodeConfig[serviceKey].values():
                feature = feature.copy()
                if feature['code'] == 'TRIPS':
                    feature['config']['protocol'] = FeatureCodeConstants.J4U_TRIPS_PROTOCOL
                elif feature['code'] == 'VEHICLE_LOCATOR':
                    feature['config']['location'] = FeatureCodeConstants.J4U_TRIPS_LOCATION
                    feature['config']['refresh'] = False
                featureCodes.append(feature)
        
        #if LCDV starts with 1I4Q (F1H) | 1I4T (F2X) | 1I4V (F2U) | 1I4U (F2X) | 1I4W (F2U) ==> “network”
        if re.match(r'^1I4[QTUVW]', lcdv):
            for feature in featureCodeConfig[serviceKey].values():
                feature = feature.copy()
                if feature['code'] == 'TRIPS':
                    feature['config']['protocol'] = FeatureCodeConstants.FIAT_NAV_PROTOCOL
                elif feature['code'] == 'VEHICLE_LOCATOR':
                    feature['config']['location'] = FeatureCodeConstants.FIAT_TRIPS_LOCATION
                    feature['config']['refresh'] = False
                featureCodes.append(feature)
    
    def getRegionByCountry(self, countryCode: Optional[str]) -> Optional[str]:
        """Get region by country code using market configuration"""
        return MarketConfig.getRegionByCountry(countryCode)
    
    def isAllowedLcdv(self, lcdv: str) -> bool:
        """
        Check whether an LCDV code is permitted for the EV‑Routing / EV‑Trip‑Planner feature.
        """
        excludedPrefixes = ['1CSC', '1CSJ', '1GO3', '1I4Q', '1I4V', '1I4T']
        
        for prefix in excludedPrefixes:
            if lcdv.startswith(prefix):
                return False
        
        return True
    
    def getFindMyCarFeature(
        self,
        featureCodeConfig: Dict[str, Any],
        serviceCodes: List[str]
    ) -> Optional[Dict[str, Any]]:
        """Get Find My Car feature if applicable"""
        if 'NAW01' not in serviceCodes:
            feature = next(iter(featureCodeConfig[FeatureCodeConstants.NON_NAW01].values())).copy()
            return feature
        return None
    
    def getDeepRefreshFeature(
        self,
        featureCodeConfig: Dict[str, Any],
        serviceCodes: List[str]
    ) -> Optional[Dict[str, Any]]:
        """Get Deep Refresh feature if applicable"""
        matchingValues = ['NAW01', 'NEF01', 'NAO01', 'NAO02']
        matchedValues = [v for v in matchingValues if v in serviceCodes]
        
        if matchedValues:
            feature = featureCodeConfig["VEHICLE_DEEP_REFRESH"].copy()
            return feature
        return None
    
    def getGasStationLocatorFeature(
        self,
        engineType: str,
        brand: Optional[str]
    ) -> Optional[Dict[str, Any]]:
        """Get gas station locator feature if applicable"""
        if engineType in ['ICE', 'PHEV', 'MHEV'] and brand == FeatureCodeConstants.BRAND_JEEP:
            feature = FeatureCodeConfig.getAddVehicleFeatures('GAS_STATION_LOCATOR').copy()
            return feature
        return None
    
    def getHydrogenStationLocatorFeature(
        self,
        engineType: str,
        brand: Optional[str]
    ) -> Optional[Dict[str, Any]]:
        """Get hydrogen station locator feature if applicable"""
        if engineType == 'HFCV' and brand == FeatureCodeConstants.BRAND_JEEP:
            feature = FeatureCodeConfig.getAddVehicleFeatures('HYDROGEN_STATION_LOCATOR').copy()
            return feature
        return None
    
    def getChargingStationLocatorFeature(
        self,
        engineType: str,
        lcdv: Optional[str]
    ) -> Optional[Dict[str, Any]]:
        """Get charging station locator feature if applicable"""
        if engineType in ['BEV', 'PHEV', 'HFCV']:
            feature = FeatureCodeConfig.getAddVehicleFeatures('CHARGE_STATION_LOCATOR').copy()
            return feature
        return None
    
    def getChargingStationManagementFeature(
        self,
        engineType: str,
        f2mcObj: Optional[Dict[str, Any]],
        country: Optional[str]
    ) -> Optional[Dict[str, Any]]:
        """Get charging station management feature if applicable"""
        if engineType in ['BEV', 'PHEV', 'HFCV']:
            feature = FeatureCodeConfig.getAddVehicleFeatures('CHARGING_STATION_MANAGEMENT').copy()
            enrolmentStatus = FeatureCodeConstants.getEnrolmentStatus('NO_ACCOUNT_LINKING')
            
            if f2mcObj:
                if not f2mcObj.get('isPaymentMethod'):
                    enrolmentStatus = FeatureCodeConstants.getEnrolmentStatus('NO_PAYMENT_METHOD')
                elif not f2mcObj.get('isAccountLinked'):
                    enrolmentStatus = FeatureCodeConstants.getEnrolmentStatus('NO_ACCOUNT_LINKING')
                else:
                    enrolmentStatus = FeatureCodeConstants.getEnrolmentStatus('COMPLETE')
            
            region = MarketConfig.getRegionByCountry(country) if country else FeatureCodeConstants.REGION_NAFTA
            
            if region == FeatureCodeConstants.REGION_NAFTA:
                feature['config']['partner'] = FeatureCodeConstants.CSM_PARTNER_F2MC
            else:
                feature['config']['partner'] = FeatureCodeConstants.CSM_PARTNER_TBD
            
            feature['config']['enrolmentStatus'] = enrolmentStatus
            return feature
        return None
    
    @staticmethod
    async def getSPSEligibility() -> Optional[Dict[str, Any]]:
        """Get SPS eligibility data from database"""
        try:
            logger.info("Fetching SPS eligibility data using ODM")
            
            # Get eligibility data grouped by scope (LCDV and MODEL)
            eligibility_data = await FeatureCodesService._spsEligibilityRepository.getSPSEligibilityData()
            
            logger.info("SPS eligibility data retrieved successfully", extra={
                'lcdv_count': len(eligibility_data.get('lcdv', [])),
                'model_count': len(eligibility_data.get('model', []))
            })
            
            return eligibility_data
            
        except Exception as e:
            logger.error(f"Failed to fetch SPS eligibility data: {str(e)}")
            return None
    
    @staticmethod
    async def spsEligibility(
        lcdv: str,
        corvetAttributes: List[str]
    ) -> Optional[Dict[str, Any]]:
        """Check SmartPhone Station eligibility"""
        try:
            feature = FeatureCodeConfig.getFeatureConfig('SMARTPHONE_STATION')['SMARTPHONE_STATION'].copy()
            
            # Get SPS Eligibility rules from BO
            sps_eligibility_data = await FeatureCodesService.getSPSEligibility()
            if not sps_eligibility_data:
                logger.error("No response for SPS Eligibility")
                return None
            
            lcdv_eligibility = sps_eligibility_data.get('lcdv', [])
            
            if not lcdv:
                return None
            
            lcdv4 = lcdv[:4]
            
            # Find eligibility row for this LCDV
            matching_eligibility = None
            for eligibility in lcdv_eligibility:
                codes = eligibility.get('codes', [])
                if codes and lcdv4 in codes:
                    matching_eligibility = eligibility
                    break
            
            if not matching_eligibility:
                return None
            
            eligibility_rule = matching_eligibility.get('eligibilityRule')
            is_eligibility_rule_found = (
                not eligibility_rule or 
                eligibility_rule in corvetAttributes
            )
            
            if not is_eligibility_rule_found:
                return None
            
            # Build filtered attributes map for quick lookup
            filtered_attributes = {
                'DRC': None,
                'DME': None,
                'DLX': None,
                'DZZ': None
            }
            
            for value in corvetAttributes:
                for prefix in filtered_attributes.keys():
                    if value.startswith(prefix):
                        filtered_attributes[prefix] = value
            
            vehicle_code = lcdv4
            
            if filtered_attributes.get('DZZ'):
                dzz_value = filtered_attributes['DZZ'][3:5]  # substr(DZZ, 3, 2)
                
                if dzz_value in ['0V', '01']:
                    vehicle_code = f"{lcdv4}_{dzz_value}"
            
            feature['config']['vehicleCode'] = vehicle_code
            feature['config']['type'] = matching_eligibility.get('type', 'spsGeneric')
            feature['config']['eligibilityDisclaimer'] = (
                matching_eligibility.get('eligibilityDisclaimer', '').lower() == 'yes'
            )
            
            return feature
            
        except Exception as e:
            logger.error(f"Error in spsEligibility: {str(e)}")
            return None
    
    @staticmethod
    async def cleanup():
        """Cleanup resources"""
        if FeatureCodesService._httpClient:
            await FeatureCodesService._httpClient.close()
            FeatureCodesService._httpClient = None


# Create global singleton instance
featureCodesService = FeatureCodesService()

import logging
from typing import Dict, Any, Optional, Union, List
from app.helper.response import WSResponse
from app.connector.sysCorvetConnector import sysCorvetConnector
from app.schemas.vehicleSchemas import ErrorDetail, ErrorResponse
from fastapi import status

logger = logging.getLogger(__name__)


class CorvetService:
    """Singleton service for Corvet data operations"""
    
    _instance: Optional['CorvetService'] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @staticmethod
    async def getCorvetData(vin: str) -> WSResponse:
        """Get Corvet data for a VIN"""
        try:
            logger.info(f"CorvetService.getCorvetData {vin}")
            return await sysCorvetConnector.get(f"/v1/corvet/{vin}/data")
        except Exception as e:
            logger.error(f"CorvetService.getCorvetData : Caught Exception {str(e)}")
            return WSResponse(getattr(e, "code", 500), str(e))
    
    @staticmethod
    async def checkVinExists(corvetData: Dict[str, Any]) -> Union[bool, ErrorResponse]:
        """Check if VIN exists in Corvet system"""
        vehicleExists = (
            corvetData.get("VEHICULE", {}).get("@attributes", {}).get("Existe", "N")
        )
        
        if vehicleExists == "N":
            logger.warning(
                "CorvetService.checkVinExists : Vehicle does not exist in Corvet system",
                extra={"corvetResponse": corvetData}
            )
            return ErrorResponse(
                error=ErrorDetail(message="Vehicle not found in Corvet system"),
                statusCode=status.HTTP_404_NOT_FOUND,
            )
        return True
    
    @staticmethod
    async def getLcdv(corvetData: Optional[Dict[str, Any]]) -> Optional[str]:
        """Get LCDV from Corvet data"""
        try:
            if not corvetData:
                return None
            return (
                corvetData.get("VEHICULE", {})
                .get("DONNEES_VEHICULE", {})
                .get("LCDV_BASE", False)
            )
        except Exception as e:
            logger.error(
                "CorvetService.getLcdv : Error while getting lcdv from corvet data",
                extra={"corvetData": corvetData, "error": str(e)}
            )
            return False
    
    @staticmethod
    async def getManagedAttributes(attributes: List[str]) -> Optional[List[str]]:
        """Get managed attributes from list"""
        managedAttributes = []
        for attribute in attributes:
            prefix = attribute[:3]
            match prefix:
                case "DCX" | "DXD" | "DCD" | "DRE" | "DRC" | "DMW" | "DVQ" | "DJY" | "D7Q" | "D7K" | "DME" | "DE2" | "DZZ" | "DLX" | "DO9" | "D32":
                    managedAttributes.append(attribute.strip())
                case "DYR":
                    if attribute[3:5] == "17":
                        managedAttributes.append(attribute.strip())
        return managedAttributes if managedAttributes else None
    
    @staticmethod
    async def validateVin(vin: str) -> bool:
        """Validate VIN format"""
        # VIN should be 17 characters
        return len(vin) == 17 and vin.isalnum()


# Create global singleton instance
corvetService = CorvetService()
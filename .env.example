# Application Configuratio
DEBUG=false
HOST=0.0.0.0
PORT=8000

# MongoDB Configuration
MONGO_DB_URL=mongodb://localhost:27017
MONGODB_DB=space_middleware
REQUEST_TTL=3600

# Microservice URLs
MS_SYS_OMNI_URL=http://localhost:8001
MS_SYS_CORVET_DATA_URL=http://localhost:8002
MS_SYS_IDP_URL=http://localhost:8003
MS_MYM1_SYS_USER_DATA_URL=http://localhost:8004
MS_SYS_SDPR_URL=http://localhost:8005
MY_MARQUE_PROC_ME_URL=http://localhost:8006
MS_SYS_SAMS_DATA_URL=http://localhost:8007
MS_SYS_USER_DB_URL=http://localhost:8008
SPACE_MIDDLEWARE_API_KEY=your-api-key-here
SETTINGS_CDN_URL=https://space-settings-preprod.space.awsmpsa.com/app/configuration/settings.json

# AWS Configuration
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
MESSENGER_TRANSPORT_DSN=sqs://localhost
AWS_SQS_REFRESH_VEHICLES_SUBSCRIPTION_QUEUE_NAME=refresh-vehicles-subscription
AWS_SQS_REFRESH_VEHICLES_NAME=refresh-vehicles
AWS_SQS_CONSUMER_RIGHTS_QUEUE=consumer-rights

# E-Routes
EROUTES_DEFAULT_ANDROID_LINK=https://play.google.com/store/apps
EROUTES_DEFAULT_IOS_LINK=https://apps.apple.com

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379
